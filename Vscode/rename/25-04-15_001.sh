#!/bin/bash

# Set locale for character handling (important for \p{Han} in tools like Perl)
export LANG=C.UTF-8

# --- Function to Extract Text from Filename (Rule D using Perl) ---
# Input: Basename of the file/folder
# Output: Extracted text snippet (Rule D) or empty string
# Rule D: Keep all chars between the first (Alpha/Han) and last (Alpha/Han).
extract_text() {
    local basename="$1"
    local filename_no_ext
    local extracted

    # Remove extension (last part after dot)
    if [[ "$basename" == *.* ]]; then
        filename_no_ext="${basename%.*}"
    else
        filename_no_ext="$basename"
    fi

    # Use Perl for robust Unicode handling (Alpha + Han)
    extracted=$(echo "$filename_no_ext" | perl -CS -pe 's/^[^\p{Alpha}\p{Han}]+//; s/[^\p{Alpha}\p{Han}]+$//')

    # Sanitize extracted_text slightly for filename (replace / or other problematic chars if needed)
    extracted=$(echo "$extracted" | tr '/' '_')
    # Add more sanitization if needed

    # Return the extracted text, or empty if nothing suitable was found
    echo "$extracted"
}
# --- End Function ---


# Set IFS for reading paths correctly
IFS=$'\n'

# Read folder paths from KM variable
folder_paths_string="$KMVAR_Local_FolderPaths"

# If no folders were selected, exit gracefully.
if [ -z "$folder_paths_string" ]; then
  echo "没有选择文件夹，操作中止。"
  exit 0
fi

# Process each selected folder path
for target_dir in $folder_paths_string; do
  echo "--- 开始处理文件夹: $target_dir ---"

  # Check if the target is a directory
  if [ ! -d "$target_dir" ]; then
    echo "错误: 文件夹 '$target_dir' 不存在或不是一个目录。"
    continue
  fi

  # --- 1. 深度清理：删除隐藏文件 (非递归) ---
  echo "正在清理隐藏文件..."
  find "$target_dir" -maxdepth 1 -type f -name '.*' -print0 | xargs -0 rm -f
  echo "隐藏文件清理完成。"

  # --- 2. 智能重命名 ---
  echo "正在准备重命名..."

  # Create a temporary file
  temp_file=$(mktemp)
  trap 'rm -f "$temp_file"' EXIT

  # Gather item information
  find "$target_dir" -maxdepth 1 \! -path "$target_dir" \! -name '.*' | while IFS= read -r item_path; do
    create_timestamp=$(stat -f "%B" "$item_path")
    formatted_date=$(date -r "$create_timestamp" "+%y-%m-%d")
    item_type="f" && [ -d "$item_path" ] && item_type="d"
    item_basename=$(basename "$item_path")
    extracted_text=$(extract_text "$item_basename")
    classification="non_textual"
    if [ -n "$extracted_text" ]; then
        classification="textual"
    fi
    echo "$item_path|$create_timestamp|$item_type|$formatted_date|$classification|$extracted_text" >> "$temp_file"
  done

  # Check if any items were found
  if [ ! -s "$temp_file" ]; then
      echo "文件夹内没有找到需要重命名的项目。"
      echo "--- 文件夹处理完成: $target_dir ---"
      continue
  fi

  echo "正在处理非文字项目 (Pass 1)..."
  # --- Pass 1: Process Non-Textual Items ---
  grep '|non_textual|' "$temp_file" | sort -t'|' -k4,4 -k2,2n | {
    current_date=""
    seq_num=1
    while IFS='|' read -r old_path timestamp item_type date_str classification _; do
      if [ "$date_str" != "$current_date" ]; then
        current_date="$date_str"
        seq_num=1
      fi
      seq_formatted=$(printf "%03d" "$seq_num")
      parent_dir=$(dirname "$old_path")
      old_basename=$(basename "$old_path")
      new_basename=""
      extension=""
      if [ "$item_type" == "f" ]; then
          if [[ "$old_basename" == *.* ]] && [ "${old_basename##*.}" != "$old_basename" ]; then
              extension=".${old_basename##*.}"
          fi
          new_basename="${date_str}_${seq_formatted}${extension}"
      else
          new_basename="${date_str}_${seq_formatted}"
      fi
      new_path="${parent_dir}/${new_basename}"

      if [ "$old_path" == "$new_path" ]; then
          echo "跳过 (非文字): '$old_basename' 名称已符合格式。"
      elif [ -e "$new_path" ]; then
          echo "警告 (非文字): 目标名称 '$new_basename' 已存在，跳过重命名 '$old_basename'"
      else
          echo "重命名 (非文字): '$old_basename' -> '$new_basename'"
          mv -n "$old_path" "$new_path" || echo "错误: 重命名 '$old_basename' 失败。"
      fi
      seq_num=$((seq_num + 1))
    done
  } # End non-textual processing block

  echo "正在处理文字项目 (Pass 2)..."
  # --- Pass 2: Process Textual Items ---
  grep '|textual|' "$temp_file" | sort -t'|' -k6,6 -k2,2n | {
    current_extracted_text=""
    seq_num=1
    while IFS='|' read -r old_path timestamp item_type date_str classification extracted_text; do
      if [ "$extracted_text" != "$current_extracted_text" ]; then
        current_extracted_text="$extracted_text"
        seq_num=1
      fi
      seq_formatted=$(printf "%03d" "$seq_num")
      parent_dir=$(dirname "$old_path")
      old_basename=$(basename "$old_path")
      new_basename=""
      extension=""
      if [ "$item_type" == "f" ]; then
          if [[ "$old_basename" == *.* ]] && [ "${old_basename##*.}" != "$old_basename" ]; then
              extension=".${old_basename##*.}"
          fi
          new_basename="${date_str}_${extracted_text}_${seq_formatted}${extension}"
      else
          new_basename="${date_str}_${extracted_text}_${seq_formatted}"
      fi
      new_path="${parent_dir}/${new_basename}"

      if [ "$old_path" == "$new_path" ]; then
          echo "跳过 (文字): '$old_basename' 名称已符合格式。"
      elif [ -e "$new_path" ]; then
          echo "警告 (文字): 目标名称 '$new_basename' 已存在，跳过重命名 '$old_basename'"
      else
          echo "重命名 (文字): '$old_basename' -> '$new_basename'"
          mv -n "$old_path" "$new_path" || echo "错误: 重命名 '$old_basename' 失败。"
      fi
      seq_num=$((seq_num + 1))
    done
  } # End textual processing block

  # --- Pass 3: Adjust Single-Item Textual Groups ---
  echo "正在调整单项文字组的序号 (Pass 3)..."
  # Find items potentially renamed in Pass 2 that end with _001 or _001.extension
  # Pattern: Date_Text_001[.Ext] -> ??-??-??_*_001*
  find "$target_dir" -maxdepth 1 \( -name '??-??-??_*_001' -o -name '??-??-??_*_001.*' \) | while IFS= read -r item_001_path; do
      # Ensure it's a file or directory (find should already do this, but double check)
      if [ ! -e "$item_001_path" ]; then
          continue
      fi

      parent_dir=$(dirname "$item_001_path")
      basename_001=$(basename "$item_001_path")

      # Extract the part before "_001"
      # Example: "24-07-27_MyText_001.txt" -> "24-07-27_MyText"
      # Example: "24-07-27_MyDir_001" -> "24-07-27_MyDir"
      name_prefix="${basename_001%_001*}"

      # Extract the extension *if* it exists after "_001"
      # Example: "24-07-27_MyText_001.txt" -> ".txt"
      # Example: "24-07-27_MyDir_001" -> ""
      temp_ext="${basename_001#*_001}" # Get part after _001, e.g., ".txt" or ""
      extension=""
      if [[ "$temp_ext" == .* ]]; then # Check if it starts with a dot
          extension="$temp_ext"
      fi

      # Construct the potential name for the second item
      potential_002_basename="${name_prefix}_002${extension}"
      potential_002_path="${parent_dir}/${potential_002_basename}"

      # Construct the desired name without the sequence number
      new_basename_no_seq="${name_prefix}${extension}"
      new_path_no_seq="${parent_dir}/${new_basename_no_seq}"

      # Check if the second item exists
      if [ ! -e "$potential_002_path" ]; then
          # It's a single item group, perform the rename to remove _001
          if [ "$item_001_path" != "$new_path_no_seq" ]; then # Avoid renaming to itself
             # Check if the target name (without _001) somehow already exists
             if [ -e "$new_path_no_seq" ]; then
                 echo "警告 (调整序号): 目标名称 '$new_basename_no_seq' 已存在，跳过重命名 '$basename_001'"
             else
                 echo "调整序号: '$basename_001' -> '$new_basename_no_seq'"
                 mv -n "$item_001_path" "$new_path_no_seq" || echo "错误: 调整序号 '$basename_001' 失败。"
             fi
          fi
      # else
          # echo "调试: '$basename_001' 存在对应的 '$potential_002_basename'，保留序号。" # Optional debug line
      fi
  done
  echo "序号调整完成。"
  # --- End Pass 3 ---


  # Temp file is automatically removed by the trap
  echo "重命名处理完成。"
  echo "--- 文件夹处理完成: $target_dir ---"
  echo # Add a blank line for readability

# Feed the multi-line variable content
done < <(echo "$folder_paths_string")

echo "所有选定文件夹处理完毕。"

exit 0