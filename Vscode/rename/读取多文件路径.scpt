# --- AppleScript Start ---
try
	tell application "System Events"
		activate
		set chosenFolders to choose folder with prompt "请拖放或选择一个或多个需要整理的文件夹：" with multiple selections allowed
	end tell
	
	set folderPathsText to ""
	repeat with aFolder in chosenFolders
		# 确保路径是 POSIX 格式 (以 / 分隔)
		set folderPathsText to folderPathsText & (POSIX path of aFolder) & linefeed
	end repeat
	
	# 移除最后一个换行符
	if folderPathsText ends with linefeed then
		set folderPathsText to text 1 thru -2 of folderPathsText
	end if
	
	# 返回以换行符分隔的路径列表
	return folderPathsText
on error errMsg number errNum
	# 如果用户取消选择，则返回空字符串，让后续脚本知道停止
	if errNum is -128 then # User canceled
		return ""
	else
		display dialog "选择文件夹时出错: " & errMsg buttons {"好的"} default button "好的"
		return "" # 返回空字符串表示出错
	end if
end try
# --- AppleScript End ---