#!/bin/bash

# 需求3：提示用户输入大文件夹路径
read -p "请输入大文件夹路径: " input_dir
dir=$(eval echo "$input_dir")

# 转换为绝对路径并进入目录
dir=$(cd "$dir" && pwd) || exit 1

# 遍历所有非隐藏的子文件夹
find "$dir" -mindepth 1 -maxdepth 1 -type d ! -name '.*' -print0 | while IFS= read -r -d '' subdir; do
    folder_name=$(basename "$subdir")
    count=1

    # 按文件名排序处理非隐藏文件
    find "$subdir" -maxdepth 1 -type f ! -name '.*' -print0 | sort -z | while IFS= read -r -d '' file; do
        # 提取文件名和扩展名
        filename=$(basename -- "$file")
        extension="${filename##*.}"
        
        # 处理无扩展名情况
        if [[ "$filename" == "$extension" ]]; then
            new_name="${folder_name}_$(printf "%03d" $count)"
        else
            new_name="${folder_name}_$(printf "%03d" $count).${extension}"
        fi

        # 移动并重命名文件
        mv -v -- "$file" "$dir/$new_name"
        ((count++))
    done

    # 删除子文件夹（强制删除包含剩余内容）
    rm -rf "$subdir"
done

echo "操作完成！所有文件已重命名，子文件夹已删除。"