{"mcpServers": {"browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"], "description": "Browser automation and web interaction tools"}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop/学习工作/学习/Typora"], "description": "File system operations within allowed directories"}, "quickchart-server": {"command": "npx", "args": ["-y", "@gongrzhe/quickchart-mcp-server"], "description": "Chart generation using QuickChart service"}, "mcp-server-chart": {"command": "npx", "args": ["-y", "@antv/mcp-server-chart"], "description": "Advanced chart generation using AntV library"}, "excel-stdio": {"command": "uvx", "args": ["excel-mcp-server", "stdio"], "description": "Excel file operations and data processing"}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "Structured problem-solving and reasoning tools"}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-ed9395205e1d4276a0546ec582b3e2f5"}, "description": "Advanced web scraping and crawling capabilities"}, "trends-hub": {"command": "npx", "args": ["-y", "mcp-trends-hub@1.6.0"], "description": "News and trending content aggregation from multiple platforms"}, "xiaohongshu-mcp": {"command": "/Users/<USER>/Redbook-Search-Comment-MCP2.0/start_xiaohongshu_mcp.sh", "args": [], "description": "<PERSON><PERSON><PERSON> (Little Red Book) social media integration"}}, "metadata": {"name": "Augment MCP Servers Configuration", "version": "1.0.0", "description": "Complete configuration for all MCP servers used in Augment Agent", "generated_date": "2025-07-29", "total_servers": 9, "categories": {"web_automation": ["browsermcp"], "file_operations": ["filesystem"], "data_visualization": ["quickchart-server", "mcp-server-chart"], "data_processing": ["excel-stdio"], "ai_reasoning": ["Sequential thinking"], "web_scraping": ["firecrawl-mcp"], "content_aggregation": ["trends-hub"], "social_media": ["xiaohongshu-mcp"]}, "usage_notes": {"browsermcp": "Requires browser to be available for automation", "filesystem": "Limited to specified directory path for security", "firecrawl-mcp": "Requires valid API key for full functionality", "xiaohongshu-mcp": "Uses custom shell script for initialization", "excel-stdio": "Uses uvx package manager instead of npx", "trends-hub": "Pinned to specific version 1.6.0"}, "security_considerations": {"filesystem": "Access restricted to /Users/<USER>/Desktop/学习工作/学习/Typora", "firecrawl-mcp": "API key should be kept secure and rotated regularly", "xiaohongshu-mcp": "Custom script execution requires proper permissions"}}}