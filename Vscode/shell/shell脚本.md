# 一、有序&无序脚本逻辑

假设你是keyboardMaestro领域的专家，请你通过shell脚本或其他方式在keyboardMaestro实现以下功能：

## 1.单独步骤-去除无用换行符

检测/n前面有无标点符号，如果没有，则替换/n为空；

## 2.添加换行符

如果检测下列序号，则在前面添加换行符：一、 二、 三、等；（一）（二）（三）等；1. 2. 3.等；（1） （2）（3）等；①②③等；a. b. c.等，●等；-等；

## 3.二次去除多余换行符

将/n/n替换为/n

## 4.排序并去除原有序号

检测换行符/n后面连接的序号，并替换为/n从而去除序号：一、 二、 三、等；（一）（二）（三）等；1. 2. 3.等；（1） （2）（3）等；①②③等；a. b. c.等，●等；-等；将原有序号去除

## 5.增加新的序号

在换行符后面，添加新的序号，可以自己选择重命名序号方式，一、 二、 三、

在换行符后面，添加新的序号，可以自己选择重命名序号方式，一、；（一）；1. ；（1）； ①；a. ；●；-；自定输入内容（当做无序列表用）





---



