<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>CFA vs CPA | 交互式职业发展指南</title>
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
<style>
body { font-family: 'Noto Sans SC', sans-serif; scroll-behavior: smooth; }
        .chart-container { position: relative; width: 100%; max-width: 700px; margin-left: auto; margin-right: auto; height: 350px; max-height: 400px; }
        。chart-container { position: relative; width: 100%; max-width: 700px; margin-left: auto; margin-right: auto; height: 350px; max-height: 400px; }
@media (max-width: 768px) { .chart-container { height: 300px; max-height: 350px; } }
        .nav-link { transition: color 0.3s, border-bottom-color 0.3s; }
        .nav-link:hover { color: #0891b2; }
        .nav-link.active { color: #0e7490; border-bottom-color: #0e7490; }
        。nav-link { transition: color 0.3s, border-bottom-color 0.3s; }
        。nav-link:hover { color: #0891b2; }
        。nav-link.active { color: #0e7490; border-bottom-color: #0e7490; }
#deep-dive .tab-btn { transition: all 0.3s ease; }
#deep-dive .tab-btn.active { color: #c0a062; border-bottom-color: #c0a062; }
#deep-dive .tab-btn:not(.active) { border-bottom-color: transparent; }
#pathways .tab-btn.active { background-color: #0e7490; color: white; }
#work-tabs .tab-btn.active { background-color: #0e7490; color: white; }
        .content-pane { display: none; }
        .content-pane.active { display: block; }
        .fade-in { animation: fadeIn 0.5s ease-in-out; }
        。content-pane { display: none; }
        。content-pane.active { display: block; }
        。fade-in { animation: fadeIn 0.5s ease-in-out; }
@keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
        .card-advantage { transition: transform 0.3s ease, box-shadow 0.3s ease; }
        .card-advantage:hover { transform: translateY(-5px); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
        。card-advantage { transition: transform 0.3s ease, box-shadow 0.3s ease; }
        。card-advantage:hover { transform: translateY(-5px); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
</style>
</head>
<body class="bg-slate-50 text-slate-800">

<header class="bg-white/80 backdrop-blur-lg shadow-sm sticky top-0 z-50">
<nav class="container mx-auto px-4 sm:px-6 lg:px-8">
<div class="flex items-center justify-between h-16">
<div class="flex items-center">
<span class="font-bold text-xl text-cyan-800">CFA vs CPA</span>
</div>
<div class="hidden md:block">
<div class="ml-10 flex items-baseline space-x-4">
<a href="#at-a-glance" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600 border-b-2 border-transparent">核心定位速览</a>
<a href="#deep-dive" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600 border-b-2 border-transparent">多维度深度对比</a>
<a href="#pathways" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600 border-b-2 border-transparent">职业发展路径</a>
<a href="#guide" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-slate-600 border-b-2 border-transparent">报考指南</a>
</div>
</div>
<div class="md:hidden">
<select id="mobile-nav" class="bg-white border border-slate-300 text-slate-700 text-sm rounded-lg focus:ring-cyan-500 focus:border-cyan-500 block w-full p-2.5">
<option value="#at-a-glance">核心定位速览</option>
<option value="#deep-dive">多维度深度对比</option>
<option value="#pathways">职业发展路径</option>
<option value="#guide">报考指南</option>
</select>
</div>
</div>
</nav>
</header>

<main class="container mx-auto p-4 sm:p-6 lg:p-8">

<section class="text-center pt-8 pb-12">
<h1 class="text-4xl md:text-5xl font-bold tracking-tight text-cyan-900 mb-4">赋能未来金融领袖</h1>
<p class="max-w-3xl mx-auto text-lg text-slate-600">一个交互式指南，深入剖析为何CFA是在现代金融领域规划职业生涯的卓越选择。</p>
</section>

<section id="at-a-glance" class="py-16 bg-white rounded-2xl shadow-lg mb-16 scroll-mt-16">
<div class="px-4 sm:px-6 lg:px-8">
<h2 class="text-2xl md:text-3xl font-bold text-center mb-8 text-slate-900">核心定位速览</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
<div class="bg-cyan-50 p-8 rounded-xl shadow-md border border-cyan-100 text-center">
<div class="text-5xl mb-4">📈</div>
<h3 class="text-2xl font-bold mb-2 text-cyan-800">CFA (特许金融分析师)</h3>
<p class="text-lg font-semibold mb-4 text-cyan-700">投资决策的战略家</p>
<span class="inline-block bg-cyan-100 text-cyan-800 rounded-full px-4 py-1 text-sm font-semibold mb-2">#前瞻性</span>
                        <p class="text-slate-600">专注于投资分析、资产估值和投资组合管理，旨在通过专业判断创造未来价值。</p>
</div>
<div class="bg-amber-50 p-8 rounded-xl shadow-md border border-amber-100 text-center">
<div class="text-5xl mb-4">🧾</div>
<h3 class="text-2xl font-bold mb-2 text-amber-800">CPA (注册会计师)</h3>
<p class="text-lg font-semibold mb-4 text-amber-700">财务报告的守护者</p>
<span class="inline-block bg-amber-100 text-amber-800 rounded-full px-4 py-1 text-sm font-semibold mb-2">#回顾性</span>
<p class="text-slate-600">专注于财务报告、审计和税务合规，<br>确保财务信息的准确性、透明度和合法性。</p>
</div>
</div>
</div>
</section>

<section id="deep-dive" class="py-16 bg-white rounded-2xl shadow-lg mb-16 scroll-mt-16">
<div class="px-4 sm:px-6 lg:px-8">
<h2 class="text-2xl md:text-3xl font-bold text-center mb-8 text-slate-900">多维度深度对比</h2>
<div class="border-b border-gray-200 mb-6">
<nav class="flex justify-center -mb-px space-x-4 md:space-x-8" aria-label="Tabs">
<button class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-lg" data-tab="salary">薪酬潜力</button>
<button class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-lg" data-tab="effort">投入与难度</button>
<button class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 font-medium text-lg" data-tab="knowledge">知识体系</button>
</nav>
</div>

<div id="tab-content">
<div id="salary-content" class="content-pane hidden">
<p class="text-center text-[#606f7b] mb-8 max-w-3xl mx-auto">CFA证书在投资领域的专业性，直接体现在其更高的薪酬潜力和高级职位的可及性上。<br>数据显示，CFA持证人在职业生涯中的收入水平显著高于CPA。</p>
<div class="text-center mb-6">
<button id="avg-salary-btn" class="bg-[#c0a062] text-white px-4 py-2 rounded-md shadow hover:bg-[#a98b54] transition-colors">平均薪酬对比</button>
<button id="senior-salary-btn" class="bg-gray-200 text-[#2c3e50] px-4 py-2 rounded-md hover:bg-gray-300 transition-colors">高级职位薪酬</button>
</div>
<div class="chart-container">
<canvas id="salaryChart"></canvas>
</div>
</div>

<div id="effort-content" class="content-pane hidden">
<p class="text-center text-[#606f7b] mb-8 max-w-3xl mx-auto">获得任何一项顶级认证都需要巨大的投入。CFA以其深度和广度要求更长的学习时间，但其全球统一的标准和灵活的报考条件为大学生提供了便利。</p>
<div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
<div class="chart-container h-64 md:h-80">
<canvas id="effortChart"></canvas>
</div>
<div class="space-y-4">
<div class="bg-gray-100 p-4 rounded-lg text-center">
<p class="text-lg font-medium text-[#606f7b]">CFA 一级通过率</p>
<p class="text-3xl font-bold text-[#c0a062]">&lt; 50%</p>
</div>
<div class="bg-gray-100 p-4 rounded-lg text-center">
<p class="text-lg font-medium text-[#606f7b]">CPA 单科通过率</p>
<p class="text-3xl font-bold text-[#2c3e50]">45-55%</p>
</div>
<div class="bg-gray-100 p-4 rounded-lg text-center">
<p class="text-lg font-medium text-[#606f7b]">大学生可报考</p>
<p class="text-2xl font-bold text-[#c0a062]">CFA (大二即可)</p>
</div>
</div>
</div>
</div>

<div id="knowledge-content" class="content-pane hidden">
<p class="text-center text-[#606f7b] mb-8 max-w-3xl mx-auto">CFA课程深入投资核心，而CPA则广泛覆盖会计商业法规。</p>
<div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto">
<div>
<h4 class="text-xl font-bold mb-4 text-[#c0a062]">CFA 知识体系核心</h4>
<ul class="space-y-2 text-[#2c3e50]">
<li class="flex items-center"><span class="text-lg mr-2">💎</span> 投资工具与资产类别</li>
<li class="flex items-center"><span class="text-lg mr-2">💎</span> 股票、固收、衍生品分析</li>
<li class="flex items-center"><span class="text-lg mr-2">💎</span> 另类投资（私募、对冲基金）</li>
<li class="flex items-center"><span class="text-lg mr-2">💎</span> 投资组合管理与财富规划</li>
<li class="flex items-center"><span class="text-lg mr-2">💎</span> 道德与专业标准</li>
<li class="flex items-center"><span class="text-lg mr-2">💎</span> 实践技能模块 (PSMs)</li>
</ul>
</div>
<div>
<h4 class="text-xl font-bold mb-4 text-[#2c3e50]">CPA 知识体系核心</h4>
<ul class="space-y-2 text-[#2c3e50]">
<li class="flex items-center"><span class="text-lg mr-2">📚</span> 审计与鉴证</li>
<li class="flex items-center"><span class="text-lg mr-2">📚</span> 财务会计与报告 (U.S. GAAP)</li>
<li class="flex items-center"><span class="text-lg mr-2">📚</span> 法规 (美国税法等)</li>
<li class="flex items-center"><span class="text-lg mr-2">📚</span> 商业环境与概念</li>
<li class="flex items-center"><span class="text-lg mr-2">📚</span> 州特定执业要求</li>
<li class="flex items-center"><span class="text-lg mr-2">📚</span> 信息技术与数据分析</li>
</ul>
</div>
</div>
</div>
</div>
</div>
</section>

<section id="why-cfa" class="mb-12">
<h2 class="text-2xl md:text-3xl font-bold text-center mb-2">为何CFA是投资领域的更优选？</h2>
<p class="text-center text-lg text-[#606f7b] mb-8">对于立志在投资界大展拳脚的你，CFA提供了四大核心优势。</p>
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
<div class="card-advantage bg-white p-6 rounded-xl shadow-md border border-gray-100">
<div class="text-4xl mb-3">🌍</div>
<h4 class="font-bold text-lg mb-2">全球通行证</h4>
<p class="text-sm text-[#606f7b]">CFA是全球投资行业公认的最高标准，为你开启国际职业生涯，连接全球金融精英网络。</p>
</div>
<div class="card-advantage bg-white p-6 rounded-xl shadow-md border border-gray-100">
<div class="text-4xl mb-3">🔑</div>
<h4 class="font-bold text-lg mb-2">顶尖机构敲门砖</h4>
<p class="text-sm text-[#606f7b]">顶级投行、基金公司高度认可CFA价值，将其视为衡量专业能力与职业道德的关键标尺。</p>
</div>
<div class="card-advantage bg-white p-6 rounded-xl shadow-md border border-gray-100">
<div class="text-4xl mb-3">🧠</div>
<h4 class="font-bold text-lg mb-2">赋能实战决策</h4>
<p class="text-sm text-[#606f7b]">课程设计紧贴实战，通过案例分析培养批判性思维和复杂投资问题的解决能力。</p>
</div>
<div class="card-advantage bg-white p-6 rounded-xl shadow-md border border-gray-100">
<div class="text-4xl mb-3">🎓</div>
<h4 class="font-bold text-lg mb-2">赢在大学起点</h4>
<p class="text-sm text-[#606f7b]">大二下即可报考，助你实现学业与职业的无缝衔接，抢占就业先机。</p>
</div>
</div>
</section>

<section id="pathways" class="py-16 bg-white rounded-2xl shadow-lg mb-16 scroll-mt-16">
<div class="px-4 sm:px-6 lg:px-8">
<div class="text-center mb-12">
<h2 class="text-3xl font-bold text-slate-900">CFA赋能多元化职业发展路径</h2>
<p class="mt-4 text-lg text-slate-600">无论您的目标是学术深造、服务于公共部门，还是驰骋于金融市场，CFA都能为您提供强大的助力。<br>请选择您感兴趣的方向，探索CFA如何为您铺就成功之路。</p>
</div>

<div class="flex justify-center flex-wrap gap-2 md:gap-4 mb-8" id="pathways-tabs">
<button data-target="path-study" class="tab-btn px-4 py-2 text-sm md:text-base font-semibold rounded-full bg-slate-200 text-slate-700">考研/留学</button>
<button data-target="path-gov" class="tab-btn px-4 py-2 text-sm md:text-base font-semibold rounded-full bg-slate-200 text-slate-700">考公/监管</button>
<button data-target="path-work" class="tab-btn px-4 py-2 text-sm md:text-base font-semibold rounded-full bg-slate-200 text-slate-700">求职就业</button>
</div>

<div id="pathways-content" class="max-w-4xl mx-auto">
<div id="path-study" class="content-pane fade-in">
<h3 class="text-2xl font-bold text-slate-800 mb-4">助力学术深造</h3>
<p class="text-slate-600 mb-4">CFA资格，尤其是通过一级考试，对申请国内外顶尖金融硕士和MBA项目有显著优势。</p>
<ul class="space-y-3">
<li class="flex items-start"><span class="flex-shrink-0 h-6 w-6 text-xl text-cyan-600">🎓</span><span class="ml-3 text-slate-700"><strong class="font-semibold">课程高度重叠：</strong>与众多金融硕士/MBA课程内容相关，可能获得课程豁免或免除GMAT/GRE。</span></li>
<li class="flex items-start"><span class="flex-shrink-0 h-6 w-6 text-xl text-cyan-600">🏆</span><span class="ml-3 text-slate-700"><strong class="font-semibold">提升录取几率：</strong>向招生官展示您对金融行业的坚定承诺、专业素养和自主学习能力。</span></li>
<li class="flex items-start"><span class="flex-shrink-0 h-6 w-6 text-xl text-cyan-600">🌐</span><span class="ml-3 text-slate-700"><strong class="font-semibold">增强全球流动性：</strong>CFA的全球认可度是申请海外名校的有力"通行证"，CFA协会的大学合作项目（UAP）遍布全球。</span></li>
</ul>
</div>
<div id="path-gov" class="content-pane fade-in">
<h3 class="text-2xl font-bold text-slate-800 mb-4">服务公共部门</h3>
<p class="text-slate-600 mb-4">CFA的知识体系和道德标准在政府财政部门、金融监管机构（如证监会、银保监会）等岗位中极具价值。</p>
<ul class="space-y-3">
<li class="flex items-start"><span class="flex-shrink-0 h-6 w-6 text-xl text-cyan-600">⚖️</span><span class="ml-3 text-slate-700"><strong class="font-semibold">恪守道德准则：</strong>CFA对职业道德的极高要求，与公务员体系的价值观高度契合。</span></li>
<li class="flex items-start"><span class="flex-shrink-0 h-6 w-6 text-xl text-cyan-600">🛡️</span><span class="ml-3 text-slate-700"><strong class="font-semibold">精通金融法规与风险管理：</strong>能够有效应对复杂的监管环境，为制定和实施金融政策、防范系统性风险做出贡献。</span></li>
<li class="flex items-start"><span class="flex-shrink-0 h-6 w-6 text-xl text-cyan-600">🏛️</span><span class="ml-3 text-slate-700"><strong class="font-semibold">增强政策洞察力：</strong>对金融市场的深刻理解，能为监管政策的制定提供宝贵视角。</span></li>
</ul>
</div>
<div id="path-work" class="content-pane fade-in">
<h3 class="text-2xl font-bold text-slate-800 mb-4">精准匹配金融岗位</h3>
<p class="text-slate-600 mb-4">CFA是进入核心金融领域的敲门砖。对于本科生而言，通过一级考试即可显著提升在咨询、银行、券商等领域的求职竞争力。<br>请选择下方行业查看具体岗位匹配：</p>
<div class="flex justify-center flex-wrap gap-2 md:gap-3 mb-6" id="work-tabs">
<button data-target="work-consulting" class="tab-btn px-3 py-1 text-sm font-medium rounded-full bg-slate-100 text-slate-600">咨询</button>
<button data-target="work-banking" class="tab-btn px-3 py-1 text-sm font-medium rounded-full bg-slate-100 text-slate-600">银行</button>
<button data-target="work-securities" class="tab-btn px-3 py-1 text-sm font-medium rounded-full bg-slate-100 text-slate-600">券商/资管</button>
</div>
<div id="work-content" class="bg-slate-100 p-6 rounded-lg min-h-[200px]">
<div id="work-consulting" class="content-pane fade-in">
<h4 class="font-bold text-lg text-slate-700 mb-2">咨询行业</h4>
<p class="text-slate-600 text-sm">CFA的分析、估值和战略思维能力是咨询工作的核心。本科生通过一级可申请：</p>
<ul class="list-disc list-inside mt-2 text-slate-600 text-sm space-y-1">
<li>商业分析师 / 研究助理</li>
<li>初级财务/战略咨询顾问</li>
</ul>
</div>
<div id="work-banking" class="content-pane fade-in">
<h4 class="font-bold text-lg text-slate-700 mb-2">银行业（投行/商行）</h4>
<p class="text-slate-600 text-sm">CFA知识覆盖投行业务（IPO, M&A）及商行核心（信贷, 风控）。本科生通过一级可申请：</p>
<ul class="list-disc list-inside mt-2 text-slate-600 text-sm space-y-1">
<li>投资银行部分析师 / 实习生</li>
<li>信贷分析师 / 风险管理助理</li>
</ul>
</div>
<div id="work-securities" class="content-pane fade-in">
<h4 class="font-bold text-lg text-slate-700 mb-2">券商 / 资产管理</h4>
<p class="text-slate-600 text-sm">这是CFA最核心的领域，知识体系与岗位需求完美契合。本科生通过一级可申请：</p>
<ul class="list-disc list-inside mt-2 text-slate-600 text-sm space-y-1">
<li>研究部分析师助理 / 行业研究员</li>
<li>基金/投资组合经理助理</li>
<li>销售与交易助理</li>
</ul>
</div>
</div>
</div>
</div>
</div>
</section>

<section id="guide" class="py-16 scroll-mt-16">
<div class="text-center mb-12">
<h2 class="text-3xl font-bold text-slate-900">CFA一级报考指南（最新）</h2>
<p class="mt-4 text-lg text-slate-600">您无需等到毕业。根据CFA协会最新规定，本科生最早可在毕业前23个月内报名参加一级考试，为您的职业生涯抢占先机。</p>
</div>
<div class="max-w-3xl mx-auto bg-white p-8 rounded-xl shadow-lg border border-cyan-100">
<h3 class="font-bold text-xl text-cyan-900 mb-4">报名资格要求</h3>
<p class="text-slate-600 mb-6">要注册并报名CFA项目，您必须持有有效的国际旅行护照，并满足以下<strong class="text-cyan-800">任一</strong>条件：</p>
<div class="space-y-4">
<div class="flex items-start">
<div class="flex-shrink-0 w-8 h-8 rounded-full bg-cyan-100 text-cyan-700 flex items-center justify-center font-bold">1</div>
<div class="ml-4">
<h4 class="font-semibold text-slate-800">拥有学士学位</h4>
<p class="text-slate-500 text-sm">已完成本科学业并取得学位证书。</p>
</div>
</div>
<div class="flex items-start">
<div class="flex-shrink-0 w-8 h-8 rounded-full bg-cyan-100 text-cyan-700 flex items-center justify-center font-bold">2</div>
<div class="ml-4">
<h4 class="font-semibold text-slate-800">在校本科生</h4>
<p class="text-slate-500 text-sm">您的毕业月份需在报名参加的一级考试窗口月份之后的11个月或更短时间内。简单来说，最早可于<strong class="text-cyan-800">毕业前23个月</strong>报名。</p>
</div>
</div>
<div class="flex items-start">
<div class="flex-shrink-0 w-8 h-8 rounded-full bg-cyan-100 text-cyan-700 flex items-center justify-center font-bold">3</div>
<div class="ml-4">
<h4 class="font-semibold text-slate-800">拥有专业工作经验</h4>
<p class="text-slate-500 text-sm">拥有至少4,000小时的相关专业工作经验（不要求与投资相关），或教育和工作经验累计达到4,000小时。</p>
</div>
</div>
</div>
<div class="mt-8 text-center bg-cyan-50 p-4 rounded-lg">
<p class="text-cyan-800 font-medium">💡 <strong>核心提示:</strong> 对于大多数本科生而言，条件2是最直接的路径。<br>这意味着您可以在大二或大三就开始准备并报名CFA一级考试！</p>
</div>
</div>
</section>

</main>

<footer class="text-center mt-16 border-t pt-8 pb-16">
<h3 class="text-xl font-bold">结论与建议</h3>
<p class="max-w-3xl mx-auto mt-4 text-[#606f7b]">如果你对投资管理充满热情，渴望在金融市场中扮演积极的战略决策者角色，那么CFA无疑是更值得投入的"长期职业投资"。它的专业深度、全球认可度和高薪潜力，将为你的职业生涯奠定坚实的基础。</p>
<a href="https://www.cfainstitute.org" target="_blank" class="mt-6 inline-block bg-[#2c3e50] text-white font-bold py-3 px-8 rounded-lg shadow-lg hover:bg-[#4a5568] transition-all">探索CFA官方网站</a>
</footer>

<script>
document.addEventListener('DOMContentLoaded', () => {

const salaryData = {
avg: {
labels: ['CFA 持证人 (学士学位)', 'CPA 持证人'],
data: [162500, 119000],
title: '平均年薪对比 (美元)'
},
senior: {
labels: ['首席投资官 (CIO)', '股票投资组合经理', '固收投资组合经理'],
data: [334500, 316600, 253250],
title: 'CFA 高级职位年薪中位数 (美元)'
}
};

const effortData = {
labels: ['CFA (总计)', 'CPA (总计)'],
hours: [900, 1900],
costs: [5635, 3000] 
};

let salaryChart, effortChart;

const chartOptions = (titleText) => ({
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: { display: false },
title: { display: true, text: titleText, font: { size: 16, family: 'Noto Sans SC' }, color: '#2c3e50' },
tooltip: {
callbacks: {
label: function(context) {
let label = context.dataset.label || '';
if (label) { label += ': '; }
if (context.parsed.y !== null) {
label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0 }).format(context.parsed.y);
}
return label;
}
}
}
},
scales: { y: { beginAtZero: true, ticks: { callback: function(value) { return '$' + (value / 1000) + 'k'; } } } }
});

const horizontalChartOptions = (titleText) => ({
indexAxis: 'y',
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: { display: false },
title: { display: true, text: titleText, font: { size: 14, family: 'Noto Sans SC' }, color: '#2c3e50', position: 'bottom' },
tooltip: {
callbacks: {
label: function(context) {
let label = context.dataset.label || '';
if (label) { label += ': '; }
if (context.parsed.x !== null) {
if (context.dataset.label === '学习小时') {
label += context.parsed.x + ' 小时';
} else {
label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0 }).format(context.parsed.x);
}
}
return label;
}
}
}
},
scales: { x: { beginAtZero: true } }
});


function createSalaryChart(type) {
const ctx = document.getElementById('salaryChart')?.getContext('2d');
if(!ctx) return;
const data = salaryData[type];
if (salaryChart) salaryChart.destroy();
salaryChart = new Chart(ctx, {
type: 'bar',
data: {
labels: data.labels,
datasets: [{
label: '年薪 (USD)', data: data.data,
backgroundColor: ['#c0a062', '#2c3e50', '#a98b54'],
borderColor: ['#a98b54', '#1e2b37', '#8e7345'],
borderWidth: 1, borderRadius: 5,
}]
},
options: chartOptions(data.title)
});
}

function createEffortChart() {
const ctx = document.getElementById('effortChart')?.getContext('2d');
if(!ctx) return;
if (effortChart) effortChart.destroy();
effortChart = new Chart(ctx, {
type: 'bar',
data: {
labels: effortData.labels,
datasets: [
{ label: '学习小时', data: effortData.hours, backgroundColor: '#c0a062', borderColor: '#a98b54', borderWidth: 1 },
{ label: '平均花费 (USD)', data: effortData.costs, backgroundColor: '#2c3e50', borderColor: '#1e2b37', borderWidth: 1 }
]
},
options: horizontalChartOptions('预计学习时间与平均花费对比')
});
}

function setupTabs(tabButtonsSelector, contentPanesSelector, initialActiveId) {
const tabButtons = document.querySelectorAll(tabButtonsSelector);
const contentPanes = document.querySelectorAll(contentPanesSelector);

if (tabButtons.length === 0) return;

tabButtons.forEach(button => {
button.addEventListener('click', () => {
const targetId = button.dataset.tab || button.dataset.target;

// Handle button active states
tabButtons.forEach(btn => {
btn.classList.remove('active');
if (btn.closest('#deep-dive')) {
btn.classList.remove('text-[#c0a062]', 'border-[#c0a062]');
btn.classList.add('text-slate-600', 'border-transparent');
} else {
btn.classList.remove('bg-cyan-700', 'text-white');
btn.classList.add('bg-slate-200', 'text-slate-700');
}
});

button.classList.add('active');
if (button.closest('#deep-dive')) {
button.classList.remove('text-slate-600', 'border-transparent');
button.classList.add('text-[#c0a062]', 'border-[#c0a062]');
} else {
button.classList.remove('bg-slate-200', 'text-slate-700');
button.classList.add('bg-cyan-700', 'text-white');
}

// Handle content pane visibility
contentPanes.forEach(pane => {
pane.classList.remove('active', 'fade-in');
pane.classList.add('hidden');
});

const targetPane = document.getElementById(targetId + '-content') || document.getElementById(targetId);
if (targetPane) {
targetPane.classList.remove('hidden');
targetPane.classList.add('active', 'fade-in');
}

// Handle chart rendering for deep-dive tabs
if (button.closest('#deep-dive')) {
if (targetId === 'salary') {
if (!salaryChart || !document.body.contains(salaryChart.canvas)) {
createSalaryChart('avg');
} else {
salaryChart.update();
}
}
if (targetId === 'effort') {
if (!effortChart || !document.body.contains(effortChart.canvas)) {
createEffortChart();
} else {
effortChart.update();
}
}
}
});
});

const initialButton = document.querySelector(`${tabButtonsSelector}[data-tab="${initialActiveId}"], ${tabButtonsSelector}[data-target="${initialActiveId}"]`);
if (initialButton) {
initialButton.click();
}
}

setupTabs('#deep-dive .tab-btn', '#tab-content .content-pane', 'salary');
setupTabs('#pathways-tabs .tab-btn', '#pathways-content .content-pane', 'path-study');
setupTabs('#work-tabs .tab-btn', '#work-content .content-pane', 'work-consulting');

document.getElementById('avg-salary-btn')?.addEventListener('click', () => createSalaryChart('avg'));
document.getElementById('senior-salary-btn')?.addEventListener('click', () => createSalaryChart('senior'));

const mobileNav = document.getElementById('mobile-nav');
if (mobileNav) {
mobileNav.addEventListener('change', function() {
const targetElement = document.querySelector(this.value);
if (targetElement) {
targetElement.scrollIntoView({ behavior: 'smooth' });
}
});
}

const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const id = entry.target.getAttribute('id');
document.querySelectorAll('.nav-link').forEach(link => {
link.classList.remove('active');
if(link.getAttribute('href') === `#${id}`) {
link.classList.add('active');
}
});
if (mobileNav) {
mobileNav.value = `#${id}`;
}
}
});
}, { rootMargin: "-50% 0px -50% 0px" });

document.querySelectorAll('main section').forEach(section => {
observer.observe(section);
});
});
</script>
</body>
</html>