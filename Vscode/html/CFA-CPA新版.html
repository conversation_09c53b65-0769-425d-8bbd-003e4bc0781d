<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>CFA vs CPA | 交互式职业发展指南</title>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
/* ===== CSS变量系统 ===== */
:root {
    /* 主色调 - 专业蓝色系 */
    --primary-blue: #1F3B8A;
    --primary-blue-light: #3B5998;
    --primary-blue-dark: #152B5F;

    /* 点缀色 - 活力橙色系 */
    --accent-orange: #FF6B35;
    --accent-orange-light: #FF8A5B;
    --accent-orange-dark: #E55A2B;

    /* 文字颜色层级 */
    --text-primary: #2C3E50;
    --text-secondary: #5A6C7D;
    --text-light: #8A9BA8;
    --text-white: #FFFFFF;

    /* 玻璃拟态背景 */
    --glass-bg-primary: rgba(255, 255, 255, 0.08);
    --glass-bg-secondary: rgba(255, 255, 255, 0.12);
    --glass-bg-hover: rgba(255, 255, 255, 0.18);

    /* 玻璃边框 */
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-border-hover: rgba(255, 255, 255, 0.3);

    /* 阴影系统 */
    --glass-shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.08);
    --glass-shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.12);
    --glass-shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.15);
    --glass-shadow-hover: 0 25px 80px rgba(0, 0, 0, 0.2);

    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 40px;
}

/* ===== 基础样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Roboto', 'Poppins', '思源黑体', system-ui, -apple-system, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    scroll-behavior: smooth;
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 50%, var(--primary-blue-light) 100%);
    min-height: 100vh;
    color: var(--text-primary);
}

/* ===== 字体层级系统 ===== */
h1 {
    font-size: 48px;
    font-weight: 700;
    color: var(--text-white);
    margin-bottom: 8px;
}

h2 {
    font-size: 36px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

h3 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

h4 {
    font-size: 18px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

p {
    font-size: 16px;
    font-weight: 300;
    color: var(--text-secondary);
}

/* ===== 玻璃拟态基础容器 ===== */
.glass-container {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--glass-bg-primary);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow-medium);
    margin: 20px;
    padding: 40px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.glass-card {
    position: relative;
    background: var(--glass-bg-primary);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 180px;
}

.glass-card:hover {
    transform: translateY(-6px) scale(1.02);
    background: var(--glass-bg-hover);
    border-color: var(--glass-border-hover);
    box-shadow: var(--glass-shadow-hover);
}

/* ===== 响应式字体调整 ===== */
@media (min-width: 768px) and (max-width: 1023px) {
    h1 { font-size: 36px; }
    h2 { font-size: 28px; }
    h3 { font-size: 20px; }
}

@media (max-width: 480px) {
    h1 { font-size: 24px; }
    h2 { font-size: 20px; }
    h3 { font-size: 16px; }
    h4 { font-size: 14px; }

    .glass-container {
        margin: 12px;
        padding: 20px;
    }
}
</style>
</head>
<body>

<!-- 导航栏 -->
<header class="glass-header">
    <nav class="nav-container">
        <div class="nav-content">
            <div class="nav-brand">
                <span class="brand-text">CFA vs CPA</span>
            </div>
            <div class="nav-desktop">
                <div class="nav-links">
                    <a href="#at-a-glance" class="nav-link">核心定位速览</a>
                    <a href="#deep-dive" class="nav-link">多维度深度对比</a>
                    <a href="#pathways" class="nav-link">职业发展路径</a>
                    <a href="#guide" class="nav-link">报考指南</a>
                </div>
            </div>
            <div class="nav-mobile">
                <select id="mobile-nav" class="mobile-select">
                    <option value="#at-a-glance">核心定位速览</option>
                    <option value="#deep-dive">多维度深度对比</option>
                    <option value="#pathways">职业发展路径</option>
                    <option value="#guide">报考指南</option>
                </select>
            </div>
        </div>
    </nav>
</header>

<style>
/* ===== 导航栏样式 ===== */
.glass-header {
    position: sticky;
    top: 0;
    z-index: 50;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--glass-bg-secondary);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow-soft);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.brand-text {
    font-weight: 700;
    font-size: 20px;
    color: var(--text-white);
}

.nav-desktop {
    display: none;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: var(--text-white);
    background: var(--glass-bg-hover);
    border-bottom-color: var(--accent-orange);
}

.nav-link.active {
    color: var(--text-white);
    border-bottom-color: var(--accent-orange);
}

.mobile-select {
    background: var(--glass-bg-primary);
    border: 1px solid var(--glass-border);
    color: var(--text-white);
    font-size: 14px;
    border-radius: 8px;
    padding: var(--spacing-sm) var(--spacing-md);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.mobile-select:focus {
    outline: none;
    border-color: var(--accent-orange);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
}

@media (min-width: 768px) {
    .nav-desktop {
        display: block;
    }

    .nav-mobile {
        display: none;
    }
}
</style>

<!-- 主要内容区域 -->
<main class="main-container">

    <!-- 页面标题区域 -->
    <section class="hero-section">
        <div class="hero-content">
            <h1>赋能未来金融领袖</h1>
            <p class="hero-description">一个交互式指南，深入剖析为何CFA是在现代金融领域规划职业生涯的卓越选择。</p>
        </div>
    </section>

    <!-- 核心定位速览 -->
    <section id="at-a-glance" class="content-section">
        <div class="glass-container">
            <h2 class="section-title">核心定位速览</h2>
            <div class="comparison-grid">
                <div class="comparison-card cfa-card">
                    <div class="card-icon">📈</div>
                    <h3 class="card-title">CFA (特许金融分析师)</h3>
                    <p class="card-subtitle">投资决策的战略家</p>
                    <span class="card-tag cfa-tag">#前瞻性</span>
                    <p class="card-description">专注于投资分析、资产估值和投资组合管理，旨在通过专业判断创造未来价值。</p>
                </div>
                <div class="comparison-card cpa-card">
                    <div class="card-icon">🧾</div>
                    <h3 class="card-title">CPA (注册会计师)</h3>
                    <p class="card-subtitle">财务报告的守护者</p>
                    <span class="card-tag cpa-tag">#回顾性</span>
                    <p class="card-description">专注于财务报告、审计和税务合规，<br>确保财务信息的准确性、透明度和合法性。</p>
                </div>
            </div>
        </div>
    </section>

<style>
/* ===== 主要内容区域样式 ===== */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.hero-section {
    text-align: center;
    padding: var(--spacing-xxl) 0;
}

.hero-content h1 {
    margin-bottom: var(--spacing-md);
}

.hero-description {
    max-width: 800px;
    margin: 0 auto;
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
}

.content-section {
    margin-bottom: var(--spacing-xxl);
    scroll-margin-top: 80px;
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-white);
}

.comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    max-width: 900px;
    margin: 0 auto;
}

.comparison-card {
    background: var(--glass-bg-secondary);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: var(--spacing-xl);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.comparison-card:hover {
    transform: translateY(-6px) scale(1.02);
    background: var(--glass-bg-hover);
    border-color: var(--glass-border-hover);
    box-shadow: var(--glass-shadow-hover);
}

.card-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
}

.card-title {
    color: var(--text-white);
    margin-bottom: var(--spacing-sm);
}

.card-subtitle {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.cfa-card .card-subtitle {
    color: var(--accent-orange);
}

.cpa-card .card-subtitle {
    color: var(--text-white);
}

.card-tag {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.cfa-tag {
    background: rgba(255, 107, 53, 0.2);
    color: var(--accent-orange);
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.cpa-tag {
    background: rgba(59, 89, 152, 0.2);
    color: var(--text-white);
    border: 1px solid rgba(59, 89, 152, 0.3);
}

.card-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .main-container {
        padding: var(--spacing-md);
    }

    .comparison-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

/* ===== 标签页系统样式 ===== */
.tab-navigation {
    border-bottom: 1px solid var(--glass-border);
    margin-bottom: var(--spacing-xl);
}

.tab-nav {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-bottom: -1px;
}

.tab-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: none;
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    border-bottom: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    color: var(--text-white);
    background: var(--glass-bg-hover);
    border-radius: 8px 8px 0 0;
}

.tab-btn.active {
    color: var(--accent-orange);
    border-bottom-color: var(--accent-orange);
}

.content-pane {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-pane.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-description {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-xl);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== 按钮组件样式 ===== */
.modern-button {
    background: var(--accent-orange);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    margin: 0 var(--spacing-xs);
}

.modern-button:hover {
    background: var(--accent-orange-light);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow-medium);
}

.modern-button.secondary {
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border);
    color: var(--text-white);
}

.modern-button.secondary:hover {
    background: var(--glass-bg-hover);
    border-color: var(--glass-border-hover);
}

.chart-controls {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.chart-container {
    position: relative;
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
    height: 350px;
    max-height: 400px;
}

@media (max-width: 768px) {
    .chart-container {
        height: 300px;
        max-height: 350px;
    }

    .tab-nav {
        gap: var(--spacing-md);
    }

    .tab-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 14px;
    }
}

/* ===== 投入与难度对比样式 ===== */
.effort-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.stats-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: var(--spacing-lg);
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: var(--glass-bg-hover);
    transform: translateY(-2px);
}

.stat-label {
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
}

.cfa-color {
    color: var(--accent-orange);
}

.cpa-color {
    color: var(--primary-blue-light);
}

/* ===== 知识体系对比样式 ===== */
.knowledge-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    max-width: 800px;
    margin: 0 auto;
}

.knowledge-column {
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: var(--spacing-lg);
    transition: all 0.3s ease;
}

.knowledge-column:hover {
    background: var(--glass-bg-hover);
    transform: translateY(-3px);
}

.knowledge-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.knowledge-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.knowledge-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-xs) 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.knowledge-icon {
    font-size: 16px;
    margin-right: var(--spacing-sm);
    flex-shrink: 0;
}

@media (max-width: 768px) {
    .effort-comparison {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .knowledge-comparison {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .stat-value {
        font-size: 20px;
    }
}
</style>

    <!-- 多维度深度对比 -->
    <section id="deep-dive" class="content-section">
        <div class="glass-container">
            <h2 class="section-title">多维度深度对比</h2>
            <div class="tab-navigation">
                <nav class="tab-nav">
                    <button class="tab-btn" data-tab="salary">薪酬潜力</button>
                    <button class="tab-btn" data-tab="effort">投入与难度</button>
                    <button class="tab-btn" data-tab="knowledge">知识体系</button>
                </nav>
            </div>

            <div id="tab-content" class="tab-content">
                <div id="salary-content" class="content-pane">
                    <p class="tab-description">CFA证书在投资领域的专业性，直接体现在其更高的薪酬潜力和高级职位的可及性上。<br>数据显示，CFA持证人在职业生涯中的收入水平显著高于CPA。</p>
                    <div class="chart-controls">
                        <button id="avg-salary-btn" class="modern-button primary">平均薪酬对比</button>
                        <button id="senior-salary-btn" class="modern-button secondary">高级职位薪酬</button>
                    </div>
                    <div class="chart-container">
                        <canvas id="salaryChart"></canvas>
                    </div>
                </div>

                <div id="effort-content" class="content-pane">
                    <p class="tab-description">获得任何一项顶级认证都需要巨大的投入。CFA以其深度和广度要求更长的学习时间，但其全球统一的标准和灵活的报考条件为大学生提供了便利。</p>
                    <div class="effort-comparison">
                        <div class="chart-container">
                            <canvas id="effortChart"></canvas>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <p class="stat-label">CFA 一级通过率</p>
                                <p class="stat-value cfa-color">&lt; 50%</p>
                            </div>
                            <div class="stat-card">
                                <p class="stat-label">CPA 单科通过率</p>
                                <p class="stat-value cpa-color">45-55%</p>
                            </div>
                            <div class="stat-card">
                                <p class="stat-label">大学生可报考</p>
                                <p class="stat-value cfa-color">CFA (大二即可)</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="knowledge-content" class="content-pane">
                    <p class="tab-description">CFA课程深入投资核心，而CPA则广泛覆盖会计商业法规。</p>
                    <div class="knowledge-comparison">
                        <div class="knowledge-column">
                            <h4 class="knowledge-title cfa-color">CFA 知识体系核心</h4>
                            <ul class="knowledge-list">
                                <li class="knowledge-item"><span class="knowledge-icon">💎</span> 投资工具与资产类别</li>
                                <li class="knowledge-item"><span class="knowledge-icon">💎</span> 股票、固收、衍生品分析</li>
                                <li class="knowledge-item"><span class="knowledge-icon">💎</span> 另类投资（私募、对冲基金）</li>
                                <li class="knowledge-item"><span class="knowledge-icon">💎</span> 投资组合管理与财富规划</li>
                                <li class="knowledge-item"><span class="knowledge-icon">💎</span> 道德与专业标准</li>
                                <li class="knowledge-item"><span class="knowledge-icon">💎</span> 实践技能模块 (PSMs)</li>
                            </ul>
                        </div>
                        <div class="knowledge-column">
                            <h4 class="knowledge-title cpa-color">CPA 知识体系核心</h4>
                            <ul class="knowledge-list">
                                <li class="knowledge-item"><span class="knowledge-icon">📚</span> 审计与鉴证</li>
                                <li class="knowledge-item"><span class="knowledge-icon">📚</span> 财务会计与报告 (U.S. GAAP)</li>
                                <li class="knowledge-item"><span class="knowledge-icon">📚</span> 法规 (美国税法等)</li>
                                <li class="knowledge-item"><span class="knowledge-icon">📚</span> 商业环境与概念</li>
                                <li class="knowledge-item"><span class="knowledge-icon">📚</span> 州特定执业要求</li>
                                <li class="knowledge-item"><span class="knowledge-icon">📚</span> 信息技术与数据分析</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CFA优势部分 -->
    <section id="why-cfa" class="content-section">
        <div class="glass-container">
            <h2 class="section-title">为何CFA是投资领域的更优选？</h2>
            <p class="section-subtitle">对于立志在投资界大展拳脚的你，CFA提供了四大核心优势。</p>
            <div class="advantages-grid">
                <div class="advantage-card">
                    <div class="advantage-icon">🌍</div>
                    <h4 class="advantage-title">全球通行证</h4>
                    <p class="advantage-description">CFA是全球投资行业公认的最高标准，为你开启国际职业生涯，连接全球金融精英网络。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">🔑</div>
                    <h4 class="advantage-title">顶尖机构敲门砖</h4>
                    <p class="advantage-description">顶级投行、基金公司高度认可CFA价值，将其视为衡量专业能力与职业道德的关键标尺。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">🧠</div>
                    <h4 class="advantage-title">赋能实战决策</h4>
                    <p class="advantage-description">课程设计紧贴实战，通过案例分析培养批判性思维和复杂投资问题的解决能力。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">🎓</div>
                    <h4 class="advantage-title">赢在大学起点</h4>
                    <p class="advantage-description">大二下即可报考，助你实现学业与职业的无缝衔接，抢占就业先机。</p>
                </div>
            </div>
        </div>
    </section>

<style>
/* ===== CFA优势部分样式 ===== */
.section-subtitle {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
}

.advantage-card {
    background: var(--glass-bg-secondary);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: var(--spacing-lg);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.advantage-card:hover {
    transform: translateY(-6px) scale(1.02);
    background: var(--glass-bg-hover);
    border-color: var(--glass-border-hover);
    box-shadow: var(--glass-shadow-hover);
}

.advantage-icon {
    font-size: 40px;
    margin-bottom: var(--spacing-md);
}

.advantage-title {
    color: var(--text-white);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.advantage-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
}

@media (max-width: 1024px) {
    .advantages-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .advantages-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
}

/* ===== 职业发展路径样式 ===== */
.pathways-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.pathways-tabs {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.pathway-tab-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 14px;
    font-weight: 600;
    border-radius: 25px;
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.pathway-tab-btn:hover {
    background: var(--glass-bg-hover);
    color: var(--text-white);
    transform: translateY(-2px);
}

.pathway-tab-btn.active {
    background: var(--primary-blue);
    color: var(--text-white);
    border-color: var(--primary-blue-light);
}

.pathways-content {
    max-width: 900px;
    margin: 0 auto;
}

.pathway-title {
    color: var(--text-white);
    font-size: 24px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.pathway-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
    display: flex;
    align-items: center;
    text-align: left;
}

.pathway-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pathway-item {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.pathway-item:hover {
    background: var(--glass-bg-hover);
    transform: translateX(8px);
}

.pathway-icon {
    font-size: 20px;
    margin-right: var(--spacing-md);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.pathway-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    flex: 1;
}

.pathway-text strong {
    color: var(--text-white);
    font-weight: 600;
}

/* ===== 求职就业子标签页样式 ===== */
.work-tabs {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.work-tab-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 12px;
    font-weight: 500;
    border-radius: 20px;
    background: var(--glass-bg-primary);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    transition: all 0.3s ease;
}

.work-tab-btn:hover {
    background: var(--glass-bg-hover);
    color: var(--text-white);
}

.work-tab-btn.active {
    background: var(--primary-blue);
    color: var(--text-white);
}

.work-content {
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: var(--spacing-lg);
    min-height: 200px;
}

.work-title {
    color: var(--text-white);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.work-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    text-align: left;
}

.work-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.work-list li {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.work-list li:last-child {
    border-bottom: none;
}

.work-list li:before {
    content: "•";
    color: var(--accent-orange);
    margin-right: var(--spacing-sm);
}

@media (max-width: 768px) {
    .pathways-tabs {
        gap: var(--spacing-sm);
    }

    .pathway-tab-btn {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: 12px;
    }

    .work-tabs {
        gap: var(--spacing-xs);
    }
}

/* ===== 报考指南样式 ===== */
.guide-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.guide-container {
    max-width: 800px;
    margin: 0 auto;
}

.guide-title {
    color: var(--text-white);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.guide-intro {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.guide-intro strong {
    color: var(--accent-orange);
    font-weight: 600;
}

.requirements-list {
    margin-bottom: var(--spacing-xl);
}

.requirement-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--glass-bg-secondary);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.requirement-item:hover {
    background: var(--glass-bg-hover);
    transform: translateY(-3px);
    box-shadow: var(--glass-shadow-medium);
}

.requirement-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-orange), var(--accent-orange-light));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    flex-shrink: 0;
    margin-right: var(--spacing-md);
}

.requirement-content {
    flex: 1;
}

.requirement-title {
    color: var(--text-white);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.requirement-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
}

.requirement-description strong {
    color: var(--accent-orange);
    font-weight: 600;
}

.tip-box {
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 12px;
    padding: var(--spacing-lg);
    text-align: center;
    position: relative;
}

.tip-box p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
}

.tip-box strong {
    color: var(--text-white);
    font-weight: 600;
}

@media (max-width: 768px) {
    .requirement-item {
        padding: var(--spacing-md);
    }

    .requirement-number {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }
}
</style>

    <!-- 职业发展路径 -->
    <section id="pathways" class="content-section">
        <div class="glass-container">
            <div class="pathways-header">
                <h2 class="section-title">CFA赋能多元化职业发展路径</h2>
                <p class="section-subtitle">无论您的目标是学术深造、服务于公共部门，还是驰骋于金融市场，CFA都能为您提供强大的助力。<br>请选择您感兴趣的方向，探索CFA如何为您铺就成功之路。</p>
            </div>

            <div class="pathways-tabs" id="pathways-tabs">
                <button data-target="path-study" class="pathway-tab-btn">考研/留学</button>
                <button data-target="path-gov" class="pathway-tab-btn">考公/监管</button>
                <button data-target="path-work" class="pathway-tab-btn">求职就业</button>
            </div>

            <div id="pathways-content" class="pathways-content">
                <div id="path-study" class="content-pane">
                    <h3 class="pathway-title">助力学术深造</h3>
                    <p class="pathway-description">CFA资格，尤其是通过一级考试，对申请国内外顶尖金融硕士和MBA项目有显著优势。</p>
                    <ul class="pathway-list">
                        <li class="pathway-item">
                            <span class="pathway-icon">🎓</span>
                            <span class="pathway-text"><strong>课程高度重叠：</strong>与众多金融硕士/MBA课程内容相关，可能获得课程豁免或免除GMAT/GRE。</span>
                        </li>
                        <li class="pathway-item">
                            <span class="pathway-icon">🏆</span>
                            <span class="pathway-text"><strong>提升录取几率：</strong>向招生官展示您对金融行业的坚定承诺、专业素养和自主学习能力。</span>
                        </li>
                        <li class="pathway-item">
                            <span class="pathway-icon">🌐</span>
                            <span class="pathway-text"><strong>增强全球流动性：</strong>CFA的全球认可度是申请海外名校的有力"通行证"，CFA协会的大学合作项目（UAP）遍布全球。</span>
                        </li>
                    </ul>
                </div>
                <div id="path-gov" class="content-pane">
                    <h3 class="pathway-title">服务公共部门</h3>
                    <p class="pathway-description">CFA的知识体系和道德标准在政府财政部门、金融监管机构（如证监会、银保监会）等岗位中极具价值。</p>
                    <ul class="pathway-list">
                        <li class="pathway-item">
                            <span class="pathway-icon">⚖️</span>
                            <span class="pathway-text"><strong>恪守道德准则：</strong>CFA对职业道德的极高要求，与公务员体系的价值观高度契合。</span>
                        </li>
                        <li class="pathway-item">
                            <span class="pathway-icon">🛡️</span>
                            <span class="pathway-text"><strong>精通金融法规与风险管理：</strong>能够有效应对复杂的监管环境，为制定和实施金融政策、防范系统性风险做出贡献。</span>
                        </li>
                        <li class="pathway-item">
                            <span class="pathway-icon">🏛️</span>
                            <span class="pathway-text"><strong>增强政策洞察力：</strong>对金融市场的深刻理解，能为监管政策的制定提供宝贵视角。</span>
                        </li>
                    </ul>
                </div>
                <div id="path-work" class="content-pane">
                    <h3 class="pathway-title">精准匹配金融岗位</h3>
                    <p class="pathway-description">CFA是进入核心金融领域的敲门砖。对于本科生而言，通过一级考试即可显著提升在咨询、银行、券商等领域的求职竞争力。<br>请选择下方行业查看具体岗位匹配：</p>
                    <div class="work-tabs" id="work-tabs">
                        <button data-target="work-consulting" class="work-tab-btn">咨询</button>
                        <button data-target="work-banking" class="work-tab-btn">银行</button>
                        <button data-target="work-securities" class="work-tab-btn">券商/资管</button>
                    </div>
                    <div id="work-content" class="work-content">
                        <div id="work-consulting" class="content-pane">
                            <h4 class="work-title">咨询行业</h4>
                            <p class="work-description">CFA的分析、估值和战略思维能力是咨询工作的核心。本科生通过一级可申请：</p>
                            <ul class="work-list">
                                <li>商业分析师 / 研究助理</li>
                                <li>初级财务/战略咨询顾问</li>
                            </ul>
                        </div>
                        <div id="work-banking" class="content-pane">
                            <h4 class="work-title">银行业（投行/商行）</h4>
                            <p class="work-description">CFA知识覆盖投行业务（IPO, M&A）及商行核心（信贷, 风控）。本科生通过一级可申请：</p>
                            <ul class="work-list">
                                <li>投资银行部分析师 / 实习生</li>
                                <li>信贷分析师 / 风险管理助理</li>
                            </ul>
                        </div>
                        <div id="work-securities" class="content-pane">
                            <h4 class="work-title">券商 / 资产管理</h4>
                            <p class="work-description">这是CFA最核心的领域，知识体系与岗位需求完美契合。本科生通过一级可申请：</p>
                            <ul class="work-list">
                                <li>研究部分析师助理 / 行业研究员</li>
                                <li>基金/投资组合经理助理</li>
                                <li>销售与交易助理</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 报考指南 -->
    <section id="guide" class="content-section">
        <div class="guide-header">
            <h2 class="section-title">CFA一级报考指南（最新）</h2>
            <p class="section-subtitle">您无需等到毕业。根据CFA协会最新规定，本科生最早可在毕业前23个月内报名参加一级考试，为您的职业生涯抢占先机。</p>
        </div>
        <div class="glass-container guide-container">
            <h3 class="guide-title">报名资格要求</h3>
            <p class="guide-intro">要注册并报名CFA项目，您必须持有有效的国际旅行护照，并满足以下<strong>任一</strong>条件：</p>
            <div class="requirements-list">
                <div class="requirement-item">
                    <div class="requirement-number">1</div>
                    <div class="requirement-content">
                        <h4 class="requirement-title">拥有学士学位</h4>
                        <p class="requirement-description">已完成本科学业并取得学位证书。</p>
                    </div>
                </div>
                <div class="requirement-item">
                    <div class="requirement-number">2</div>
                    <div class="requirement-content">
                        <h4 class="requirement-title">在校本科生</h4>
                        <p class="requirement-description">您的毕业月份需在报名参加的一级考试窗口月份之后的11个月或更短时间内。简单来说，最早可于<strong>毕业前23个月</strong>报名。</p>
                    </div>
                </div>
                <div class="requirement-item">
                    <div class="requirement-number">3</div>
                    <div class="requirement-content">
                        <h4 class="requirement-title">拥有专业工作经验</h4>
                        <p class="requirement-description">拥有至少4,000小时的相关专业工作经验（不要求与投资相关），或教育和工作经验累计达到4,000小时。</p>
                    </div>
                </div>
            </div>
            <div class="tip-box">
                <p>💡 <strong>核心提示:</strong> 对于大多数本科生而言，条件2是最直接的路径。<br>这意味着您可以在大二或大三就开始准备并报名CFA一级考试！</p>
            </div>
        </div>
    </section>

</main>

<!-- 页脚 -->
<footer class="footer-section">
    <div class="glass-container footer-container">
        <h3 class="footer-title">结论与建议</h3>
        <p class="footer-description">如果你对投资管理充满热情，渴望在金融市场中扮演积极的战略决策者角色，那么CFA无疑是更值得投入的"长期职业投资"。它的专业深度、全球认可度和高薪潜力，将为你的职业生涯奠定坚实的基础。</p>
        <a href="https://www.cfainstitute.org" target="_blank" class="cta-button">探索CFA官方网站</a>
    </div>
</footer>

<style>
/* ===== 页脚样式 ===== */
.footer-section {
    margin-top: var(--spacing-xxl);
    padding-bottom: var(--spacing-xxl);
}

.footer-container {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.footer-title {
    color: var(--text-white);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto var(--spacing-xl) auto;
    line-height: 1.6;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
    color: var(--text-white);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: 12px;
    text-decoration: none;
    box-shadow: var(--glass-shadow-medium);
    transition: all 0.3s ease;
    border: 1px solid var(--glass-border);
}

.cta-button:hover {
    background: linear-gradient(135deg, var(--primary-blue-light), var(--primary-blue));
    transform: translateY(-3px);
    box-shadow: var(--glass-shadow-hover);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', () => {

const salaryData = {
avg: {
labels: ['CFA 持证人 (学士学位)', 'CPA 持证人'],
data: [162500, 119000],
title: '平均年薪对比 (美元)'
},
senior: {
labels: ['首席投资官 (CIO)', '股票投资组合经理', '固收投资组合经理'],
data: [334500, 316600, 253250],
title: 'CFA 高级职位年薪中位数 (美元)'
}
};

const effortData = {
labels: ['CFA (总计)', 'CPA (总计)'],
hours: [900, 1900],
costs: [5635, 3000] 
};

let salaryChart, effortChart;

const chartOptions = (titleText) => ({
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: { display: false },
title: { display: true, text: titleText, font: { size: 16, family: 'Inter' }, color: '#FFFFFF' },
tooltip: {
callbacks: {
label: function(context) {
let label = context.dataset.label || '';
if (label) { label += ': '; }
if (context.parsed.y !== null) {
label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0 }).format(context.parsed.y);
}
return label;
}
}
}
},
scales: {
    y: {
        beginAtZero: true,
        ticks: {
            callback: function(value) { return '$' + (value / 1000) + 'k'; },
            color: '#FFFFFF'
        },
        grid: { color: 'rgba(255, 255, 255, 0.1)' }
    },
    x: {
        ticks: { color: '#FFFFFF' },
        grid: { color: 'rgba(255, 255, 255, 0.1)' }
    }
}
});

const horizontalChartOptions = (titleText) => ({
indexAxis: 'y',
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: { display: false },
title: { display: true, text: titleText, font: { size: 14, family: 'Inter' }, color: '#FFFFFF', position: 'bottom' },
tooltip: {
callbacks: {
label: function(context) {
let label = context.dataset.label || '';
if (label) { label += ': '; }
if (context.parsed.x !== null) {
if (context.dataset.label === '学习小时') {
label += context.parsed.x + ' 小时';
} else {
label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 0 }).format(context.parsed.x);
}
}
return label;
}
}
}
},
scales: {
    x: {
        beginAtZero: true,
        ticks: { color: '#FFFFFF' },
        grid: { color: 'rgba(255, 255, 255, 0.1)' }
    },
    y: {
        ticks: { color: '#FFFFFF' },
        grid: { color: 'rgba(255, 255, 255, 0.1)' }
    }
}
});


function createSalaryChart(type) {
const ctx = document.getElementById('salaryChart')?.getContext('2d');
if(!ctx) return;
const data = salaryData[type];
if (salaryChart) salaryChart.destroy();
salaryChart = new Chart(ctx, {
type: 'bar',
data: {
labels: data.labels,
datasets: [{
label: '年薪 (USD)', data: data.data,
backgroundColor: ['#FF6B35', '#1F3B8A', '#FF8A5B'],
borderColor: ['#E55A2B', '#152B5F', '#FF6B35'],
borderWidth: 1, borderRadius: 8,
}]
},
options: chartOptions(data.title)
});
}

function createEffortChart() {
const ctx = document.getElementById('effortChart')?.getContext('2d');
if(!ctx) return;
if (effortChart) effortChart.destroy();
effortChart = new Chart(ctx, {
type: 'bar',
data: {
labels: effortData.labels,
datasets: [
{ label: '学习小时', data: effortData.hours, backgroundColor: '#FF6B35', borderColor: '#E55A2B', borderWidth: 1 },
{ label: '平均花费 (USD)', data: effortData.costs, backgroundColor: '#1F3B8A', borderColor: '#152B5F', borderWidth: 1 }
]
},
options: horizontalChartOptions('预计学习时间与平均花费对比')
});
}

function setupTabs(tabButtonsSelector, contentPanesSelector, initialActiveId) {
const tabButtons = document.querySelectorAll(tabButtonsSelector);
const contentPanes = document.querySelectorAll(contentPanesSelector);

if (tabButtons.length === 0) return;

tabButtons.forEach(button => {
button.addEventListener('click', () => {
const targetId = button.dataset.tab || button.dataset.target;

// Handle button active states
tabButtons.forEach(btn => {
btn.classList.remove('active');
});

button.classList.add('active');

// Handle content pane visibility
contentPanes.forEach(pane => {
pane.classList.remove('active');
pane.style.display = 'none';
});

const targetPane = document.getElementById(targetId + '-content') || document.getElementById(targetId);
if (targetPane) {
targetPane.style.display = 'block';
targetPane.classList.add('active');
}

// Handle chart rendering for deep-dive tabs
if (button.closest('#deep-dive')) {
if (targetId === 'salary') {
if (!salaryChart || !document.body.contains(salaryChart.canvas)) {
createSalaryChart('avg');
} else {
salaryChart.update();
}
}
if (targetId === 'effort') {
if (!effortChart || !document.body.contains(effortChart.canvas)) {
createEffortChart();
} else {
effortChart.update();
}
}
}
});
});

const initialButton = document.querySelector(`${tabButtonsSelector}[data-tab="${initialActiveId}"], ${tabButtonsSelector}[data-target="${initialActiveId}"]`);
if (initialButton) {
initialButton.click();
}
}

setupTabs('#deep-dive .tab-btn', '#tab-content .content-pane', 'salary');
setupTabs('#pathways-tabs .pathway-tab-btn', '#pathways-content .content-pane', 'path-study');
setupTabs('#work-tabs .work-tab-btn', '#work-content .content-pane', 'work-consulting');

document.getElementById('avg-salary-btn')?.addEventListener('click', () => createSalaryChart('avg'));
document.getElementById('senior-salary-btn')?.addEventListener('click', () => createSalaryChart('senior'));

const mobileNav = document.getElementById('mobile-nav');
if (mobileNav) {
mobileNav.addEventListener('change', function() {
const targetElement = document.querySelector(this.value);
if (targetElement) {
targetElement.scrollIntoView({ behavior: 'smooth' });
}
});
}

const observer = new IntersectionObserver((entries) => {
entries.forEach(entry => {
if (entry.isIntersecting) {
const id = entry.target.getAttribute('id');
document.querySelectorAll('.nav-link').forEach(link => {
link.classList.remove('active');
if(link.getAttribute('href') === `#${id}`) {
link.classList.add('active');
}
});
if (mobileNav) {
mobileNav.value = `#${id}`;
}
}
});
}, { rootMargin: "-50% 0px -50% 0px" });

document.querySelectorAll('main section').forEach(section => {
observer.observe(section);
});
});
</script>
</body>
</html>