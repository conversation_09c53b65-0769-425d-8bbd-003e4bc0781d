<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华东政法大学2025届毕业生就业指南</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Warm Harmony (Background: gray-50, Text: gray-800, Primary Accent: blue-800, Secondary Accent: blue-500, Highlight: orange-500) -->
    <!-- Application Structure Plan: The SPA is a task-oriented, filter-driven dashboard. The primary user control is the 'College' selector. Within each college's view, users can apply secondary filters for 'Industry'. Job roles are presented as interactive cards in a grid. Clicking a card opens a detailed modal with JD, skills, a dynamic salary chart, and a career path diagram. This structure transforms the static report into an active exploration tool, prioritizing user-led discovery over linear reading. -->
    <!-- Visualization & Content Choices:
        1. Role Data -> Goal: Organize & Explore -> Viz: Interactive Filterable Cards (HTML/CSS/JS) -> Interaction: User selects College, then clicks Industry filters. The job grid updates instantly. Justification: More engaging and efficient than a static table. Library: Vanilla JS.
        2. Salary Data -> Goal: Compare & Inform -> Viz: Horizontal Bar Chart (Chart.js/Canvas) in detail modal and a main overview chart. -> Interaction: Modal chart dynamically renders on card click. Overview chart provides a macro view. Justification: Clear, simple visualization for numerical range comparison. Library: Chart.js.
        3. Career Path -> Goal: Inform/Organize -> Viz: Simple Flow Diagram (HTML/CSS Flexbox). -> Interaction: Static visual in modal. Justification: Lightweight and responsive, avoids unnecessary library overhead for a linear progression. Method: HTML/CSS.
        4. Market Trends -> Goal: Inform/Contextualize -> Viz: Thematic cards and summary text. -> Interaction: Static. Justification: Provides high-level context quickly. Method: HTML/CSS.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f9fafb; /* gray-50 */
            color: #1f2937; /* gray-800 */
        }
        .text-primary-accent { color: #1e3a8a; } /* blue-800 */
        .bg-primary-accent { background-color: #1e3a8a; }
        .border-primary-accent { border-color: #1e3a8a; }
        .text-secondary-accent { color: #3b82f6; } /* blue-500 */
        .bg-secondary-accent { background-color: #3b82f6; }
        .highlight-tag {
            background-color: #ffedd5; /* orange-100 */
            color: #9a3412; /* orange-800 */
            font-weight: 600;
        }

        .glass-card {
            background-color: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .btn-filter {
            transition: all 0.3s ease;
            border: 1px solid #d1d5db; /* gray-300 */
            background-color: #ffffff;
            color: #4b5563; /* gray-600 */
        }
        .btn-filter.active {
            background-color: #1e3a8a; /* blue-800 */
            color: white;
            font-weight: 700;
            border-color: #1e3a8a;
            box-shadow: 0 4px 10px rgba(30, 58, 138, 0.2);
        }
        .btn-filter:not(.active):hover {
            background-color: #f3f4f6; /* gray-100 */
            border-color: #3b82f6; /* blue-500 */
            color: #1e3a8a;
        }

        .job-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        .job-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.08);
        }
        #modal {
            transition: opacity 0.3s ease-in-out;
        }
        #modal-content {
            transition: transform 0.3s ease-in-out;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 350px;
            max-height: 450px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }
        /* Styles for path steps in modal */
        .path-step-item {
            background-color: #e0e7ff; /* indigo-100 */
            color: #1e3a8a; /* Primary Accent Blue */
            padding: 0.6rem 1rem; /* Increased padding */
            border-radius: 9999px;
            font-weight: 500;
            white-space: nowrap;
            margin: 0.3rem; /* Adjusted margin */
            font-size: 0.9rem; /* Slightly larger font */
        }
        .path-arrow {
            color: #6b7280; /* gray-500 */
            margin: 0 0.4rem; /* Adjusted margin */
            font-size: 1.1rem; /* Slightly smaller arrow for balance */
            flex-shrink: 0;
        }
        /* Styles for content truncation in job cards */
        .job-card .jd-content,
        .job-card .requirements-content,
        .job-card .path-content {
            max-height: 70px; /* Limit height to prevent cards from becoming too long */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3; /* Limit to 3 lines */
            -webkit-box-orient: vertical;
            line-height: 1.4; /* Improved line height for readability */
        }
        /* Optimized styles for path steps within job cards to prevent squeezing */
        .job-card .path-content {
            display: flex; /* Ensure flexbox is applied */
            flex-wrap: wrap; /* Allow items to wrap to the next line */
            gap: 0.25rem; /* Use gap for consistent spacing between items */
            margin: 0; /* Reset any negative margins */
            align-items: center;
        }
        .job-card .path-content .path-step-item {
            padding: 0.3rem 0.6rem; /* Slightly smaller padding for card view */
            font-size: 0.75rem; /* Smaller font for card view */
            margin: 0; /* Reset margin, rely on gap */
        }
        .job-card .path-content .path-arrow {
            font-size: 0.8rem; /* Smaller arrow for card view */
            margin: 0; /* Reset margin, rely on gap */
        }
    </style>
</head>
<body class="antialiased">

    <header class="bg-white shadow-md sticky top-0 z-40">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <h1 class="text-xl md:text-2xl font-bold text-primary-accent">
                <span class="text-secondary-accent">华政学子</span> | 2025就业指南
            </h1>
            <a href="#conclusion" class="hidden md:inline-block bg-secondary-accent text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors shadow-md">核心建议</a>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 md:py-12">

        <section class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">探索您的金融职业未来</h2>
            <p class="max-w-3xl mx-auto text-gray-600">
                本应用旨在将详尽的就业报告转化为动态的探索工具。请首先选择您的学院，然后利用下方的过滤器，发掘最适合您的行业与岗位，洞察您的独特竞争优势。
            </p>
        </section>

        <section id="college-selector" class="mb-8 text-center">
            <h3 class="text-xl font-semibold mb-4 text-gray-700">第一步：请选择您的学院</h3>
            <div class="inline-flex rounded-lg shadow-lg" role="group">
                <button type="button" id="btn-business" class="btn-filter active text-lg font-semibold py-3 px-8 rounded-l-lg">
                    <span class="mr-2">💼</span>商学院
                </button>
                <button type="button" id="btn-law" class="btn-filter text-lg font-semibold py-3 px-8 rounded-r-lg">
                    <span class="mr-2">⚖️</span>其他学院
                </button>
            </div>
        </section>

        <section id="explorer" class="glass-card p-6 md:p-8 rounded-2xl shadow-xl mb-12">
            <div class="mb-8">
                <h3 class="text-2xl font-bold mb-2 text-center text-primary-accent">职业机会浏览器</h3>
                <p class="text-center text-gray-500 max-w-2xl mx-auto">
                    第二步：通过行业偏好进行筛选。点击职位卡片可查看详细信息，包括岗位职责、能力要求、薪资水平和职业发展路径。
                </p>
            </div>
            
            <div class="flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8 mb-8">
                <div class="flex items-center gap-2">
                    <span class="font-semibold text-gray-600">行业:</span>
                    <div id="industry-filters" class="flex flex-wrap justify-center gap-2"></div>
                </div>
            </div>

            <div id="job-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"></div>
            <p id="no-results" class="text-center text-gray-500 py-8 hidden">未找到符合条件的岗位，请尝试其他筛选组合。</p>
        </section>

        <!-- Salary Overview Chart Section -->
        <section id="salary-overview" class="glass-card p-6 md:p-8 rounded-2xl shadow-lg mb-12">
            <h3 class="text-2xl font-bold mb-2 text-center text-primary-accent">2025热门岗位薪酬概览</h3>
            <p class="text-center text-gray-500 max-w-2xl mx-auto mb-6">
                薪酬是职业选择的重要考量之一。下图展示了当前学院下不同领域热门岗位的年薪范围（单位：万元人民币），帮助您直观了解市场的薪酬水平。请注意，薪酬会因经验、地域和公司而异，图中数据仅供参考。
            </p>
            <div class="chart-container">
                <canvas id="salaryOverviewChart"></canvas>
            </div>
        </section>

        <section id="insights" class="mb-12">
             <h3 class="text-2xl font-bold mb-8 text-center">核心洞察：金融行业的未来图景</h3>
             <p class="text-center text-gray-500 max-w-2xl mx-auto mb-8">
                理解宏观趋势是做出明智职业选择的第一步。当前金融行业正由三大核心力量驱动：数字化转型、合规化深化以及ESG理念的普及。这些趋势不仅重塑了岗位需求，也为具备“法商融合”背景的您提供了前所未有的机遇。
             </p>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="glass-card p-6 rounded-xl text-center">
                    <div class="text-4xl mb-2">🚀</div>
                    <h4 class="font-bold text-lg mb-2 text-secondary-accent">数字化与金融科技</h4>
                    <p class="text-sm text-gray-600">金融科技不再是口号，而是深入业务的引擎。数据分析、人工智能、算法工程等岗位需求旺盛，传统岗位也亟需数字化素养。</p>
                </div>
                <div class="glass-card p-6 rounded-xl text-center">
                    <div class="text-4xl mb-2">🛡️</div>
                    <h4 class="font-bold text-lg mb-2 text-secondary-accent">合规化与风险管理</h4>
                    <p class="text-sm text-gray-600">监管环境日益趋严，风险控制与合规管理成为金融机构的生命线。具备法律背景的金融人才在此领域优势显著。</p>
                </div>
                <div class="glass-card p-6 rounded-xl text-center">
                    <div class="text-4xl mb-2">🌱</div>
                    <h4 class="font-bold text-lg mb-2 text-secondary-accent">ESG与可持续发展</h4>
                    <p class="text-sm text-gray-600">环境、社会和公司治理(ESG)已成为投资决策和企业战略的核心。ESG投资分析、咨询等新兴岗位为求职者开辟了高价值的新赛道。</p>
                </div>
            </div>
        </section>
        
        <section id="conclusion" class="text-center bg-primary-accent text-white p-8 md:p-12 rounded-2xl shadow-2xl">
            <h3 class="text-2xl md:text-3xl font-bold mb-4">致华政学子：行动建议</h3>
            <p class="max-w-3xl mx-auto mb-6">
                金融行业的未来属于那些能够将“法商融合”的独特优势与前沿技能相结合的复合型人才。我们为您提炼了以下几点核心建议，助您在激烈的竞争中脱颖而出。
            </p>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 text-left">
                <div class="bg-white/20 p-4 rounded-lg shadow-inner">
                    <h4 class="font-bold mb-1">1. 深化法商融合</h4>
                    <p class="text-sm">在金融合规、法律风控、证券法律事务等领域，您的法律背景是不可替代的核心竞争力。请持续深化对金融业务的理解。</p>
                </div>
                <div class="bg-white/20 p-4 rounded-lg shadow-inner">
                    <h4 class="font-bold mb-1">2. 拥抱数字科技</h4>
                    <p class="text-sm">主动学习Python、SQL等数据分析工具，培养数据驱动的思维方式，将技术能力与专业知识结合。</p>
                </div>
                <div class="bg-white/20 p-4 rounded-lg shadow-inner">
                    <h4 class="font-bold mb-1">3. 考取专业认证</h4>
                    <p class="text-sm">尽早规划并考取CFA、FRM等国际认证。它们是进入投资、风控等核心岗位并获得薪酬溢价的关键。</p>
                </div>
                <div class="bg-white/20 p-4 rounded-lg shadow-inner">
                    <h4 class="font-bold mb-1">4. 积累实践经验</h4>
                    <p class="text-sm">积极利用校企合作资源，寻求在头部金融机构的实习机会，将理论知识应用于实践，丰富个人履历。</p>
                </div>
            </div>
        </section>
    </main>
    
    <div id="modal" class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center p-4 hidden opacity-0">
        <div id="modal-content" class="glass-card rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto transform scale-95">
            <div class="sticky top-0 bg-white/80 backdrop-blur-sm p-4 sm:p-6 border-b border-gray-200 flex justify-between items-center z-10">
                <h2 id="modal-title" class="text-2xl font-bold text-primary-accent"></h2>
                <button id="close-modal" class="text-gray-500 hover:text-red-500 transition-colors text-3xl font-light">&times;</button>
            </div>
            <div class="p-4 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <div class="mb-6">
                            <h3 class="font-bold text-lg mb-2 border-l-4 border-secondary-accent pl-3">岗位职责 (JD)</h3>
                            <div id="modal-jd" class="text-gray-700 text-sm leading-relaxed space-y-2"></div>
                        </div>
                        <div class="mb-6">
                            <h3 class="font-bold text-lg mb-3 border-l-4 border-secondary-accent pl-3">能力要求</h3>
                            <div id="modal-skills" class="flex flex-wrap gap-2"></div>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg mb-3 border-l-4 border-secondary-accent pl-3">典型发展路径</h3>
                            <div id="modal-path" class="flex flex-wrap items-center text-sm"></div>
                        </div>
                    </div>
                    <div>
                         <div class="mb-6 glass-card p-4 rounded-lg">
                            <h3 class="font-bold text-lg mb-3 border-l-4 border-secondary-accent pl-3">薪酬水平参考 (年薪)</h3>
                            <p id="modal-salary-text" class="text-sm text-gray-600 mb-4"></p>
                            <div class="chart-container h-48 md:h-56">
                                <canvas id="salaryChart"></canvas>
                            </div>
                        </div>
                        <div class="glass-card p-4 rounded-lg">
                            <h3 class="font-bold text-lg mb-3 border-l-4 border-secondary-accent pl-3">参考公司</h3>
                            <p id="modal-companies" class="text-gray-700 text-sm"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
const jobData = {
    business: {
        '商业银行': [
            { tier: '前台', title: '管理培训生', jd: ['旨在培养银行未来的经营管理和专业技术人才。', '经历多岗位轮岗，全面熟悉银行业务流程，为未来管理岗位做准备。'], requirements: ['综合素质良好，学习能力强', '具备管理潜质和创新意识', '有CFA/FRM证书者在金融市场、风控方向有显著优势'], salary: '6-20K/月 (应届生), 部分24万/年', salaryRange: [7, 24], path: ['基层轮岗', '核心部门定岗', '业务骨干/项目经理', '中高层管理'], companies: '中国银行、工商银行、招商银行', cert: 'cfa_frm' },
            { tier: '前台', title: '客户经理', jd: ['负责市场客户的开发、维护与服务，根据客户需求提供银行各类金融产品和综合解决方案。'], requirements: ['优秀的沟通表达能力和市场开拓能力', '人际交往能力强，有服务意识', 'CFA证书对财富管理方向有帮助'], salary: '1-4万/月', salaryRange: [12, 48], path: ['初级客户经理', '高级客户经理', '团队主管', '支行负责人'], companies: '各大商业银行', cert: 'cfa' },
            { tier: '中台', title: '风险管理/合规专员', jd: ['负责银行业务的风险识别、评估、监控和报告，确保业务运营符合内外部监管要求。', '可能涉及搭建和优化风险评分模型。'], requirements: ['敏锐的风险洞察力', '扎实的数理分析和逻辑推理能力(模型岗)', '熟悉金融法律法规', 'FRM是核心优势证书'], salary: '专家级年薪30-120万', salaryRange: [30, 120], path: ['风险/合规专员', '高级专员', '经理', '总监'], companies: '中国银行、福建海峡银行、各大行风控/合规部', cert: 'frm' },
            { tier: '中台', title: '数据分析师', jd: ['负责集团层面的数据运营、分析、应用及前沿技术研究，通过数据洞察为业务决策提供支持。'], requirements: ['熟练掌握Python/R/SQL等工具', '扎实的统计学、数学基础', '有金融机构模型开发经验者优先'], salary: '数字创新岗薪酬溢价高', salaryRange: [25, 60], path: ['数据分析师', '高级数据分析师', '数据科学家/总监'], companies: '中国银行、工商银行', cert: 'none' },
            { tier: '后台', title: '信息科技岗', jd: ['负责全行信息系统的开发、运维、网络安全及数字化建设，培养金融科技专业人才。'], requirements: ['计算机、数理、统计、金融工程等相关专业背景', '扎实的编程和算法功底', 'CFA/FRM证书有助于理解业务需求'], salary: '算法岗可达40-50万/年', salaryRange: [20, 50], path: ['IT工程师', '高级工程师', '技术项目经理', '金融科技专家/总监'], companies: '中国银行、工商银行', cert: 'cfa_frm' }
        ],
        '证券公司': [
            { tier: '前台', title: '投资经理/研究员', jd: ['对证券价值、市场变动进行深入分析，发布研究报告，或负责具体的投资组合管理。'], requirements: ['广阔的金融市场视野和深厚的专业知识', '优秀的数据敏感度和市场判断力', 'CFA是核心优势证书'], salary: '总监级年薪50-200万', salaryRange: [30, 200], path: ['研究助理', '研究员', '投资经理', '基金经理/投资总监'], companies: '中信证券、华泰证券、国投创益', cert: 'cfa' },
            { tier: '前台', title: '投资银行承做/承销岗', jd: ['承做岗负责IPO、并购等项目的材料撰写、尽职调查；承销岗负责产品的销售。'], requirements: ['CPA、法律职业资格证书是核心竞争力', '相关的实习或项目经验', 'CFA/FRM是重要加分项'], salary: '高薪领域，应届生视项目而定', salaryRange: [30, 100], path: ['分析师', '项目经理', '高级经理', '保荐代表人/董事总经理'], companies: '中信证券、中金公司', cert: 'cfa_frm' },
            { tier: '中台', title: '风险管理岗', jd: ['负责证券公司各项业务的风险管理，包括市场风险、信用风险、操作风险等，确保公司在可控风险范围内运营。'], requirements: ['扎实的数理或计算机专业功底', '有统计分析、衍生品定价模型经验者优先', 'FRM是核心优势证书'], salary: '专家级年薪30-120万', salaryRange: [30, 120], path: ['风险管理专员', '风险管理经理', '风险管理总监'], companies: '中证投保基金、上海证券交易所', cert: 'frm' },
            { tier: '中台', title: '法律合规岗', jd: ['负责公司内部法律事务、合规审查、监管沟通，确保业务活动符合法律法规。'], requirements: ['扎实的经济法、金融法专业功底', '通过法律职业资格考试者优先', 'CFA/FRM有助于理解金融业务'], salary: '高级岗跳槽增幅可观', salaryRange: [25, 80], path: ['合规专员', '高级合规专员', '合规经理', '合规/法律总监'], companies: '中证投保基金、上海证券交易所', cert: 'cfa_frm' }
        ],
        '互联网金融': [
            { tier: '前台', title: '产品经理', jd: ['负责互联网金融产品的设计、开发、迭代和生命周期管理，包括需求分析、功能设计、用户体验优化等。'], requirements: ['市场洞察力，用户需求分析能力', '良好的沟通协调和项目管理能力', 'CFA/FRM有助于理解产品逻辑和风险'], salary: '年薪18-24万', salaryRange: [18, 24], path: ['初级产品经理', '产品经理', '高级产品经理', '产品总监'], companies: '蚂蚁金服、百度金融、京东金融', cert: 'cfa_frm' },
            { tier: '中台', title: '数据分析师/算法工程师', jd: ['负责平台数据分析、用户行为建模、风险控制算法开发、智能推荐系统构建等。'], requirements: ['扎实的数学、统计学、计算机科学背景', '熟练掌握Python/R/SQL', '有金融机构模型开发经验者优先'], salary: '算法岗可达40-50万/年', salaryRange: [40, 50], path: ['初级分析师/工程师', '高级分析师/工程师', '数据科学家/算法专家'], companies: '百度、工商银行网络金融部', cert: 'none' },
            { tier: '中台', title: '风险管理岗', jd: ['负责互联网金融产品的信用、市场、操作风险管理，构建和优化风险控制模型。'], requirements: ['敏锐的风险洞察力', '熟悉金融风险管理理论和实践', 'FRM是核心优势证书'], salary: '专家级年薪30-120万', salaryRange: [30, 120], path: ['风险管理专员', '风险管理经理', '风险管理总监'], companies: '互联网金融公司风控部门', cert: 'frm' }
        ],
        '四大咨询': [
            { tier: '前台', title: '咨询顾问', jd: ['参与战略、管理、IT等各类咨询项目，进行资料搜集、数据分析、方案设计与报告撰写。'], requirements: ['出色的学习能力、逻辑分析能力和文字表达能力', '良好的团队沟通与合作能力，抗压性强', 'CFA/FRM在金融服务、风险管理咨询中有优势'], salary: '硕士应届生月薪约7.5K起', salaryRange: [10, 30], path: ['助理顾问', '咨询顾问', '高级顾问', '经理', '高级经理', '合伙人'], companies: '普华永道、德勤、安永、毕马威', cert: 'cfa_frm' },
            { tier: '中台', title: '审计师/财务咨询', jd: ['审计师负责对企业财务报表进行审计；财务咨询提供财务管理、税务筹划、并购重组等专业建议。'], requirements: ['扎实的会计、审计、财务管理、税法功底', 'CPA是核心证书，CFA/FRM有助财务咨询'], salary: '硕士应届生月薪约7.5K起', salaryRange: [10, 30], path: ['助理审计师', '审计师', '高级审计师', '经理', '合伙人'], companies: '普华永道、德勤、安永、毕马威', cert: 'cfa_frm' }
        ],
        'ESG': [
            { tier: '前台', title: 'ESG咨询顾问', jd: ['参与ESG报告编制，为客户提供ESG战略规划、指标体系搭建、评级咨询等服务。'], requirements: ['出色的学习、逻辑分析和文字表达能力', '有咨询公司经验者优先', 'CFA/FRM有助于理解ESG对财务和风险的影响'], salary: '硕士应届生年薪10-20万', salaryRange: [10, 20], path: ['助理→顾问→高级→项目经理→总监'], companies: '商道纵横、磐石绿色创新发展研究院', cert: 'cfa_frm' },
            { tier: '中台', title: 'ESG研究员/分析师', jd: ['根据客户需求，进行资料收集、实地研究、数据分析，提供可持续发展议题的洞察分析。'], requirements: ['较强的学习能力、信息和数据处理能力', '有应对气候变化领域研究或工作经验者优先', 'CFA/FRM有助于从投资和风险角度评估ESG'], salary: '硕士应届生年薪10-20万', salaryRange: [10, 20], path: ['助理→研究员→高级→专家'], companies: '商道纵横、磐石绿色创新发展研究院', cert: 'cfa_frm' }
        ],
        '基金': [
            { tier: '前台', title: '投资研究员', jd: ['对宏观经济、行业、公司进行深入研究，撰写研究报告，为基金经理的投资决策提供支持。'], requirements: ['扎实的金融、经济学功底，对研究有浓厚兴趣', '良好的数据敏感度和市场判断力', 'CFA是核心优势证书'], salary: '总监级年薪50-200万', salaryRange: [30, 200], path: ['助理→研究员→高级→基金经理助理→总监'], companies: '国泰基金、易方达基金、建信基金', cert: 'cfa' },
            { tier: '中台', title: '风险管理岗', jd: ['负责基金公司投资组合的风险识别、评估、监控和报告，确保基金运作符合监管要求和风控目标。'], requirements: ['扎实的数理分析、逻辑推理能力和建模能力', '对风险管理有浓厚兴趣', 'FRM是核心优势证书'], salary: '专家级年薪30-120万', salaryRange: [30, 120], path: ['专员→经理→总监'], companies: '各大基金公司风控部门', cert: 'frm' }
        ]
    },
    law: {
        '商业银行': [
            { tier: '中台', title: '金融合规/法律风控专员', jd: ['负责金融机构内部合规体系建设与维护，确保各项业务活动符合金融法律法规。', '专注于识别、评估和管理业务中的法律风险。'], requirements: ['扎实的法学功底，尤其经济法、金融法', '熟悉金融监管政策', 'FRM有助于从量化和全面风险管理的角度审视法律风险'], salary: '高级岗跳槽增幅可观 (15-50万/年)', salaryRange: [15, 50], path: ['合规/风控专员', '高级专员', '经理', '总监/首席合规官'], companies: '各大商业银行合规/法务部', cert: 'frm' },
            { tier: '中台', title: '反洗钱合规专员', jd: ['负责反洗钱（AML）和反恐怖融资（CFT）相关合规工作，监控可疑交易，确保业务符合国内外反洗钱法规。'], requirements: ['熟悉反洗钱相关法律法规和监管要求', '具备数据分析和风险识别能力', '良好的沟通和报告撰写能力'], salary: '12-30万/年', salaryRange: [12, 30], path: ['反洗钱专员', '高级反洗钱专员', '反洗钱经理', '合规总监'], companies: '各大商业银行合规部', cert: 'none' }
        ],
        '证券公司': [
            { tier: '中台', title: '证券法律事务专员', jd: ['负责公司在证券发行、上市、并购重组、信息披露等过程中的法律事务，起草、审查法律文件。'], requirements: ['扎实的证券法、公司法知识', '通过法律职业资格考试者优先', 'CFA/FRM有助于理解金融产品结构和交易流程'], salary: '高级岗跳槽增幅可观 (20-60万/年)', salaryRange: [20, 60], path: ['法律事务助理', '法律事务专员', '高级专员', '经理'], companies: '各大券商、基金公司法务部', cert: 'cfa_frm' },
            { tier: '中台', title: '内核/质控岗', jd: ['负责投资银行项目的内部审核与质量控制，确保项目符合法律法规和公司内控要求，防范业务风险。'], requirements: ['扎实的法律、会计、金融知识', '熟悉投行业务流程和监管规定', 'CPA或法律职业资格证书优先'], salary: '25-70万/年', salaryRange: [25, 70], path: ['内核专员', '高级内核专员', '内核经理', '内核总监'], companies: '中信证券、中金公司等券商投行部', cert: 'none' }
        ],
        '互联网金融': [
            { tier: '后台', title: 'IT治理风险合规', jd: ['负责互联网金融平台的IT治理、技术风险管理和合规性审查，确保技术系统和数据处理流程符合数据安全、隐私保护、网络安全等法规要求。'], requirements: ['熟悉IT治理框架、网络安全法规', '具备风险评估和合规审查能力'], salary: '5-10万/月 (60-120万/年)', salaryRange: [60, 120], path: ['IT合规专员', 'IT合规经理', 'IT治理风险合规总监'], companies: '润和软件、陆金所、360数科', cert: 'none' },
            { tier: '中台', title: '数据隐私合规专员', jd: ['负责数据合规体系建设，确保数据收集、存储、使用符合数据隐私法律法规（如GDPR、个人信息保护法）。'], requirements: ['熟悉数据隐私法律法规和行业标准', '具备数据安全和隐私保护意识', '良好的沟通和跨部门协作能力'], salary: '15-40万/年', salaryRange: [15, 40], path: ['数据隐私专员', '高级数据隐私专员', '数据隐私经理', '数据合规总监'], companies: '蚂蚁金服、京东金融、微众银行', cert: 'none' }
        ],
        '四大咨询': [
            { tier: '中台', title: '风险咨询顾问', jd: ['协助客户识别、评估和管理各类风险，包括运营风险、金融风险、网络安全风险、合规风险等，提供风险管理解决方案。'], requirements: ['风险管理知识、合规与监管意识、数据分析能力、解决问题能力', '通过FRM一级、二级考试或持有FRM认证将是重要加分项。'], salary: '3-8万/月 (36-96万/年)', salaryRange: [36, 96], path: ['风险咨询助理', '风险咨询顾问', '高级风险咨询顾问', '风险咨询经理'], companies: '普华永道、毕马威、德勤、安永', cert: 'frm' },
            { tier: '后台', title: '法务/合规顾问', jd: ['为客户提供法律咨询服务，协助企业建立健全法律合规体系，处理公司治理、合同审查、纠纷解决等法律事务。'], requirements: ['扎实的法律专业知识', '熟悉公司法、合同法等相关法律法规', '具备较强的沟通和问题解决能力'], salary: '20-50万/年', salaryRange: [20, 50], path: ['法务助理', '法务顾问', '高级法务顾问', '法务经理'], companies: '普华永道、德勤、安永、毕马威', cert: 'none' }
        ],
        'ESG': [
            { tier: '中台', title: 'ESG法律合规顾问', jd: ['协助企业理解并遵守国内外ESG相关法律法规和披露要求，提供ESG合规风险评估和解决方案。'], requirements: ['扎实的法律背景，熟悉环境法、公司治理法规', '对ESG标准和披露框架有深入理解', '良好的沟通和分析能力'], salary: '25-60万/年', salaryRange: [25, 60], path: ['ESG合规助理', 'ESG合规顾问', '高级ESG合规顾问', 'ESG合规经理'], companies: '商道纵横、艾华迪集团、四大咨询ESG部门', cert: 'none' }
        ],
        '基金': [
            { tier: '中台', title: '基金法律合规岗', jd: ['负责基金公司日常运营的法律合规审查，确保基金募集、投资、运作等环节符合法律法规和监管要求。'], requirements: ['扎实的法律知识，熟悉《证券投资基金法》等法规', '具备较强的风险识别和合规审查能力', '通过法律职业资格考试者优先'], salary: '20-50万/年', salaryRange: [20, 50], path: ['法律合规专员', '高级法律合规专员', '法律合规经理', '法律合规总监'], companies: '各大基金公司法务合规部', cert: 'none' }
        ]
    }
};

const collegeSelector = document.getElementById('college-selector');
const industryFiltersContainer = document.getElementById('industry-filters');
// Removed certFiltersContainer as per user request
const jobGrid = document.getElementById('job-grid');
const noResults = document.getElementById('no-results');

let currentCollege = 'business';
let currentIndustry = 'all';
// Removed currentCert as per user request

let salaryOverviewChartInstance = null; // Declare this globally

function getIndustriesForCollege(college) {
    return Object.keys(jobData[college] || {});
}

function renderFilters() {
    const industries = getIndustriesForCollege(currentCollege);
    industryFiltersContainer.innerHTML = `<button class="btn-filter py-2 px-4 rounded-full" data-filter="all">所有行业</button>` +
        industries.map(industry => `<button class="btn-filter py-2 px-4 rounded-full" data-filter="${industry}">${industry}</button>`).join('');
    
    // Removed certFiltersContainer.innerHTML as per user request

    addFilterListeners();
    updateActiveStates();
}

function renderJobPostings() {
    let jobsToRender = [];
    const industries = (currentIndustry === 'all') ? getIndustriesForCollege(currentCollege) : [currentIndustry];
    
    industries.forEach(industry => {
        const jobsInIndustry = jobData[currentCollege]?.[industry] || [];
        jobsToRender.push(...jobsInIndustry);
    });

    // Removed cert filtering logic as per user request
    const filteredJobs = jobsToRender; // All jobs are rendered now without cert filter

    jobGrid.innerHTML = '';
    if (filteredJobs.length > 0) {
        filteredJobs.forEach(job => {
            const card = document.createElement('div');
            card.className = 'job-card glass-card rounded-xl overflow-hidden flex flex-col';
            
            let certHtml = '';
            if (job.cert && job.cert.includes('cfa')) {
                certHtml += `<span class="highlight-tag text-xs font-semibold mr-2 px-2.5 py-0.5 rounded-full">CFA优先</span>`;
            }
            if (job.cert && job.cert.includes('frm')) {
                certHtml += `<span class="highlight-tag text-xs font-semibold mr-2 px-2.5 py-0.5 rounded-full">FRM优先</span>`;
            }

            const jdContent = Array.isArray(job.jd) ? job.jd.map(item => `<p>${item}</p>`).join('') : `<p>${job.jd}</p>`;
            const requirementsContent = Array.isArray(job.requirements) ? job.requirements.map(item => `<p>${item}</p>`).join('') : `<p>${job.requirements}</p>`;
            const pathContent = job.path.map((step, index) => `
                <span class="path-step-item">${step}</span>
                ${index < job.path.length - 1 ? '<span class="path-arrow">→</span>' : ''}
            `).join('');

            card.innerHTML = `
                <div class="p-5 flex-grow">
                    <div class="flex justify-between items-start mb-2">
                        <h4 class="text-lg font-bold text-gray-800">${job.title}</h4>
                        <span class="text-xs font-semibold ${job.tier === '前台' ? 'bg-orange-200 text-orange-800' : (job.tier === '中台' ? 'bg-blue-200 text-blue-800' : 'bg-gray-300 text-gray-800')} py-1 px-3 rounded-full shadow-sm whitespace-nowrap">${job.tier}</span>
                    </div>
                    <p class="text-gray-500 font-semibold mb-3 text-sm">${job.companies}</p>
                    <div class="flex flex-wrap gap-1 mb-4">
                        ${certHtml}
                    </div>

                    <div class="mb-3">
                        <h5 class="font-bold text-gray-700 text-sm mb-1">岗位职责 (JD)</h5>
                        <div class="jd-content text-sm text-gray-600">${jdContent}</div>
                    </div>

                    <div class="mb-3">
                        <h5 class="font-bold text-gray-700 text-sm mb-1">能力要求</h5>
                        <div class="requirements-content text-sm text-gray-600">${requirementsContent}</div>
                    </div>
                    
                    <div class="mb-3">
                        <h5 class="font-bold text-gray-700 text-sm mb-1">发展路径</h5>
                        <div class="path-content flex flex-wrap items-center text-sm text-primary-accent">
                            ${pathContent}
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 p-4 text-center border-t border-gray-200 mt-auto">
                    <span class="text-md font-bold text-primary-accent">${job.salary}</span>
                </div>
            `;
            card.addEventListener('click', () => openModal(job));
            jobGrid.appendChild(card);
        });
    }

    noResults.classList.toggle('hidden', filteredJobs.length > 0);
}

function renderSalaryOverviewChart() {
    const ctx = document.getElementById('salaryOverviewChart').getContext('2d');
    let allJobsForChart = [];
    for (const industry in jobData[currentCollege]) {
        allJobsForChart = allJobsForChart.concat(jobData[currentCollege][industry]);
    }

    let chartData = allJobsForChart.filter(job => job.salaryRange && job.salaryRange.length === 2).map(job => ({
        label: `${job.title} (${Object.keys(jobData[currentCollege]).find(key => jobData[currentCollege][key].includes(job))})`,
        data: job.salaryRange,
    }));

    chartData.sort((a, b) => a.data[0] - b.data[0]);

    if(salaryOverviewChartInstance) {
        salaryOverviewChartInstance.destroy();
    }
    
    salaryOverviewChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.map(d => d.label.length > 16 ? d.label.substring(0, 15) + '...' : d.label), // Truncate long labels
            datasets: [{
                label: '最低年薪 (万)',
                data: chartData.map(d => d.data[0]),
                backgroundColor: 'rgba(59, 130, 246, 0.7)', /* blue-500 with opacity */
                borderColor: 'rgba(59, 130, 246, 1)', /* blue-500 */
                borderWidth: 1,
                borderRadius: 5,
            },
            {
                label: '最高年薪 (万)',
                data: chartData.map(d => d.data[1] - d.data[0]),
                backgroundColor: 'rgba(30, 58, 138, 0.7)', /* blue-800 with opacity */
                borderColor: 'rgba(30, 58, 138, 1)', /* blue-800 */
                borderWidth: 1,
                borderRadius: 5,
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true,
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '年薪范围 (万元)',
                        font: { size: 14, weight: 'bold' }
                    }
                },
                y: {
                    stacked: true,
                    ticks: {
                        font: { size: 12 }
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: { size: 12 }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const low = context.chart.data.datasets[0].data[context.dataIndex];
                            const high = context.chart.data.datasets[1].data[context.dataIndex] + low;
                            return `${context.chart.data.labels[context.dataIndex]}: ${low} - ${high}万`;
                        }
                    }
                }
            }
        }
    });
}


function updateActiveStates() {
    document.querySelectorAll('#college-selector .btn-filter').forEach(btn => btn.classList.toggle('active', btn.id === `btn-${currentCollege}`));
    document.querySelectorAll('#industry-filters .btn-filter').forEach(btn => {
        if (btn.dataset.filter === currentIndustry) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
    // Removed cert filter active state update as per user request
}

function addFilterListeners() {
    industryFiltersContainer.querySelectorAll('.btn-filter').forEach(btn => {
        btn.addEventListener('click', () => {
            currentIndustry = btn.dataset.filter;
            updateActiveStates();
            renderJobPostings();
            renderSalaryOverviewChart(); // Update overview chart on filter change
        });
    });
    // Removed certFiltersContainer event listener as per user request
}

function initialize() {
    collegeSelector.addEventListener('click', (e) => {
        const button = e.target.closest('.btn-filter');
        if (!button) return;
        currentCollege = button.id.replace('btn-', '');
        currentIndustry = 'all';
        // Removed currentCert reset as per user request
        renderFilters();
        renderJobPostings();
        renderSalaryOverviewChart(); // Initial render for overview chart
    });

    renderFilters();
    renderJobPostings();
    renderSalaryOverviewChart(); // Initial render for overview chart
}

document.addEventListener('DOMContentLoaded', initialize);

const modal = document.getElementById('modal');
const modalContent = document.getElementById('modal-content');
const closeModalBtn = document.getElementById('close-modal');
let detailSalaryChartInstance = null;

const openModal = (job) => {
    document.getElementById('modal-title').textContent = job.title;
    document.getElementById('modal-jd').innerHTML = Array.isArray(job.jd) ? job.jd.map(item => `<p>${item}</p>`).join('') : `<p>${job.jd}</p>`;
    document.getElementById('modal-salary-text').textContent = `${job.salary}`;
    document.getElementById('modal-companies').textContent = job.companies;

    const skillsContainer = document.getElementById('modal-skills');
    skillsContainer.innerHTML = job.requirements.map(skill => {
        const isCert = skill.toLowerCase().includes('cfa') || skill.toLowerCase().includes('frm');
        return `<span class="px-3 py-1 text-xs rounded-full shadow-sm ${isCert ? 'bg-primary-accent text-white font-bold' : 'bg-gray-200 text-gray-700'}">${skill}</span>`;
    }).join('');

    const pathContainer = document.getElementById('modal-path');
    pathContainer.innerHTML = job.path.map((step, index) => `
        <span class="path-step-item">${step}</span>
        ${index < job.path.length - 1 ? '<span class="path-arrow">→</span>' : ''}
    `).join('');

    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.remove('opacity-0');
        modalContent.classList.remove('scale-95');
    }, 10);
    document.body.style.overflow = 'hidden';

    const ctx = document.getElementById('salaryChart').getContext('2d');
    if (detailSalaryChartInstance) {
        detailSalaryChartInstance.destroy();
    }
    detailSalaryChartInstance = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['年薪范围 (万元)'],
            datasets: [{
                label: '最低年薪',
                data: [job.salaryRange[0]],
                backgroundColor: 'rgba(59, 130, 246, 0.6)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1,
                borderRadius: 5,
            }, {
                label: '薪资上限',
                data: [job.salaryRange[1] - job.salaryRange[0]],
                backgroundColor: 'rgba(30, 58, 138, 0.6)',
                borderColor: 'rgba(30, 58, 138, 1)',
                borderWidth: 1,
                borderRadius: 5,
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: { beginAtZero: true, stacked: true, title: { display: true, text: '年薪 (万元)' } },
                y: { stacked: true }
            },
            plugins: {
                legend: { display: false },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const low = context.chart.data.datasets[0].data[0];
                            const high = context.chart.data.datasets[1].data[0] + low;
                            return `范围: ${low} - ${high}万元`;
                        }
                    }
                }
            }
        }
    });
};

const closeModal = () => {
    modal.classList.add('opacity-0');
    modalContent.classList.add('scale-95');
    setTimeout(() => {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }, 300);
};

closeModalBtn.addEventListener('click', closeModal);
modal.addEventListener('click', (e) => {
    if (e.target === modal) closeModal();
});

</script>
</body>
</html>
