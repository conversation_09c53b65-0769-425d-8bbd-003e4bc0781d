<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2025届上经贸大金融就业交互指南</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Google Blue (Background: blue-50, Text: gray-800, Primary Accent: blue-600, Secondary Accent: blue-400, Contrasting Accent: amber-500 for '前台') -->
    <!-- Application Structure Plan: 采用以用户为中心的设计，首先通过顶部的“学院选择器”（金融学院/统计学院）作为核心过滤器，使用户能立即进入与自己最相关的路径。随后，通过行业标签（如商行、券商）进行二次筛选，将大量信息结构化、分层展示。岗位信息以卡片形式呈现，清晰地整合了职责、要求、薪酬和发展路径，便于比较和阅读。应用还包含一个交互式薪酬图表，将报告中的薪酬数据可视化，使用户能直观比较不同岗位的收入潜力。这种“引导式探索”结构比线性报告更高效，能显著提升用户查找信息和决策的效率。 -->
    <!-- Visualization & Content Choices: 1. 报告信息：各学院及行业下的岗位详情 -> 目标：组织与告知 -> 呈现方式：HTML/Tailwind 卡片 -> 交互：通过JS根据学院和行业选择动态过滤和渲染 -> 理由：卡片式布局清晰、模块化，便于用户快速浏览和比较多个岗位。2. 报告信息：各岗位薪酬数据 -> 目标：比较 -> 呈现方式：Chart.js 水平条形图 -> 交互：图表根据所选学院动态更新，展示相关岗位的薪酬范围 -> 理由：可视化薪酬对比比纯文本更直观，帮助用户快速评估职业的经济回报。3. 报告信息：CFA/FRM证书价值 -> 目标：告知与强调 -> 呈现方式：带有图标和重点文本的专用板块 -> 交互：无，静态重点展示 -> 理由：将证书价值提炼为独立的视觉模块，能有效加深用户印象。4. 报告信息：职业发展路径 -> 目标：告知 -> 呈现方式：HTML/Tailwind 流程图样式 -> 交互：无，静态展示 -> 理由：使用简单的HTML元素和线条创建视觉流程，比纯文本列表更易理解职业晋升阶梯。 -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #e0f2fe; /* blue-50, slightly more vibrant */
            color: #374151; /* gray-800 */
        }
        .chart-container {
            position: relative;
            margin: auto;
            height: 50vh;
            width: 100%;
            max-width: 800px;
            max-height: 450px;
        }
        .nav-button {
            transition: all 0.3s ease;
            border: 1px solid #2563eb; /* blue-600 */
            background-color: white; /* Default background */
            color: #374151; /* gray-800 */
        }
        .nav-button.active {
            background-color: #2563eb; /* blue-600 */
            color: white;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }
        .industry-tag {
            background-color: white; /* Default background */
            color: #374151; /* gray-800 */
            transition: all 0.3s ease;
        }
        .industry-tag.active {
            background-color: #3b82f6; /* blue-500 */
            color: white;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .path-step {
            display: flex;
            align-items: center;
        }
        .path-step:not(:last-child)::after {
            content: '→';
            font-size: 1.5rem;
            color: #6b7280; /* gray-500 */
            margin: 0 0.75rem;
        }
        /* Glassmorphism effect */
        .glass-card {
            background-color: rgba(255, 255, 255, 0.4); /* Translucent white */
            backdrop-filter: blur(10px); /* Blur effect */
            -webkit-backdrop-filter: blur(10px); /* For Safari */
            border: 1px solid rgba(255, 255, 255, 0.3); /* Subtle border */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08); /* Soft shadow */
        }
        /* Modal styles */
        #modal {
            transition: opacity 0.3s ease-in-out;
        }
        #modal-content {
            transition: transform 0.3s ease-in-out;
        }
        .job-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        /* Path step styling for modal */
        .path-step-item {
            background-color: rgba(59, 130, 246, 0.1); /* Secondary accent with transparency */
            color: #3b82f6;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-weight: 500;
            white-space: nowrap;
            margin: 0.25rem; /* Added margin for spacing */
        }
        .path-arrow {
            color: #9ca3af; /* Neutral grey for arrows */
            margin: 0 0.5rem;
            font-size: 1.25rem;
        }
    </style>
</head>
<body class="text-gray-800">

    <div class="container mx-auto p-4 md:p-8">
        
        <header class="text-center mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-blue-700 mb-2">2025届上海对外经贸大学</h1>
            <h2 class="text-2xl md:text-3xl font-semibold text-gray-700">金融就业交互指南</h2>
        </header>

        <main>
            <section id="intro" class="mb-12 glass-card p-6 rounded-xl">
                <p class="text-lg text-center text-gray-700 leading-relaxed">
                    欢迎！本指南旨在为上海对外经贸大学的同学们提供一份清晰、实用的金融行业就业导航。在当前金融业深刻变革的背景下，理解行业趋势、精准定位是成功的关键。这里，我们为您梳理了<strong class="text-blue-600">金融学院</strong>和<strong class="text-blue-400">统计学院</strong>的核心就业方向，剖析热门岗位，并解读CFA与FRM证书如何为您的职业生涯增添砝码。请首先选择您的学院，开启探索之旅。
                </p>
            </section>

            <section id="college-selector" class="mb-8 text-center">
                <div class="inline-flex rounded-lg shadow-lg" role="group">
                    <button type="button" id="btn-finance" class="nav-button active text-lg font-semibold py-3 px-8 rounded-l-lg">
                        <span class="mr-2">🎓</span>金融学院
                    </button>
                    <button type="button" id="btn-statistics" class="nav-button text-lg font-semibold py-3 px-8 rounded-r-lg">
                        <span class="mr-2">📊</span>统计学院
                    </button>
                </div>
            </section>

            <section id="industry-tags" class="mb-8 text-center space-x-2 space-y-2">
            </section>

            <section id="job-postings" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            </section>

            <section id="salary-visualization" class="my-16 glass-card p-6 rounded-xl">
                 <h3 class="text-2xl font-bold text-center text-blue-700 mb-2">薪酬水平速览</h3>
                 <p class="text-center text-gray-600 mb-6 max-w-3xl mx-auto">本图表直观展示了不同岗位的年薪范围（单位：万元），帮助您快速了解各职业方向的收入潜力。薪酬范围综合了初级到资深级别，具体薪资会因个人能力、经验和所在公司而异。</p>
                <div class="chart-container">
                    <canvas id="salaryChart"></canvas>
                </div>
            </section>

            <section id="certification-value" class="my-16">
                 <h3 class="text-2xl font-bold text-center text-blue-700 mb-8">CFA & FRM：职业发展的黄金双翼</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="glass-card p-6 rounded-xl border-l-4 border-blue-500">
                        <h4 class="text-xl font-bold text-blue-600 mb-3">CFA (特许金融分析师)</h4>
                        <p class="text-gray-700 mb-4">被誉为“金融第一考”，CFA提供的是一套<strong class="font-semibold">全面、系统的投资决策知识体系</strong>。它覆盖从宏观经济到公司财报分析，再到资产估值和投资组合管理的全过程。</p>
                        <ul class="space-y-2 list-inside list-disc text-gray-600">
                            <li><strong class="font-semibold text-gray-700">知识广度：</strong> 构建金融全景视野，是投行、研究、资管领域的“通用语言”。</li>
                            <li><strong class="font-semibold text-gray-700">全球认可：</strong> 进军国际顶尖金融机构的“入场券”，全球雇主高度认可。</li>
                            <li><strong class="font-semibold text-gray-700">薪酬助力：</strong> 持证人通常能获得显著的薪酬溢价和更广阔的职业发展空间。</li>
                        </ul>
                    </div>
                    <div class="glass-card p-6 rounded-xl border-l-4 border-blue-400">
                        <h4 class="text-xl font-bold text-blue-500 mb-3">FRM (金融风险管理师)</h4>
                        <p class="text-gray-700 mb-4">在金融市场日益复杂的今天，FRM代表了<strong class="font-semibold">风险管理领域的最高专业水平</strong>。它专注于识别、度量和管理金融风险的各种工具和技术。</p>
                         <ul class="space-y-2 list-inside list-disc text-gray-600">
                            <li><strong class="font-semibold text-gray-700">专业深度：</strong> 深耕风险管理，是风控、合规、量化岗位的“核心武器”。</li>
                            <li><strong class="font-semibold text-gray-700">实务导向：</strong> 知识体系紧贴市场实践，能够直接应用于工作。</li>
                            <li><strong class="font-semibold text-gray-700">需求旺盛：</strong> 随着监管趋严，优秀的风险管理人才已成为各大机构争抢的对象。</li>
                        </ul>
                    </div>
                </div>
            </section>
        </main>

        <footer class="text-center mt-12 pt-6 border-t border-gray-300">
            <p class="text-gray-500">© 2025 上海对外经贸大学就业指导交互平台。数据来源：公开招聘信息及行业报告。</p>
        </footer>
    </div>

    <!-- Modal -->
    <div id="modal" class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center p-4 hidden opacity-0">
        <div id="modal-content" class="glass-card rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto transform scale-95">
            <div class="sticky top-0 bg-white/80 backdrop-blur-sm p-4 sm:p-6 border-b border-gray-200 flex justify-between items-center z-10">
                <h2 id="modal-title" class="text-2xl font-bold text-blue-600"></h2>
                <button id="close-modal" class="text-gray-500 hover:text-red-500 transition-colors text-3xl font-light">&times;</button>
            </div>
            <div class="p-4 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div>
                        <div class="mb-6">
                            <h3 class="font-bold text-lg mb-2 border-l-4 border-blue-600 pl-3">岗位职责 (JD)</h3>
                            <ul id="modal-jd" class="text-gray-600 text-sm leading-relaxed list-disc list-inside"></ul>
                        </div>
                        <div class="mb-6">
                            <h3 class="font-bold text-lg mb-3 border-l-4 border-blue-600 pl-3">能力要求</h3>
                            <div id="modal-skills" class="flex flex-wrap gap-2"></div>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg mb-3 border-l-4 border-blue-600 pl-3">典型发展路径</h3>
                            <div id="modal-path" class="flex flex-wrap items-center text-sm"></div>
                        </div>
                    </div>
                    <!-- Right Column -->
                    <div>
                         <div class="mb-6 glass-card p-4 rounded-lg">
                            <h3 class="font-bold text-lg mb-3 border-l-4 border-blue-600 pl-3">薪酬水平参考</h3>
                            <p id="modal-salary-text" class="text-sm text-gray-600 mb-4"></p>
                            <div class="chart-container h-48 md:h-56">
                                <canvas id="modal-salary-chart"></canvas>
                            </div>
                        </div>
                        <div class="glass-card p-4 rounded-lg">
                            <h3 class="font-bold text-lg mb-3 border-l-4 border-blue-600 pl-3">参考公司</h3>
                            <p id="modal-companies" class="text-gray-600 text-sm"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
const jobData = {
    finance: {
        '商业银行': [
            {
                tier: '前台',
                title: '管理培训生',
                jd: [
                    '经历系统性培养和多岗位轮岗，全面熟悉银行业务流程。',
                    '积累基层与业务条线经验，为未来管理岗位做准备。'
                ],
                requirements: [
                    '国内外重点院校本科及以上学历，专业不限，金融、法律、STEM优先。',
                    '优秀的学习、沟通能力和团队合作精神。',
                    '有知名企业实习经历者优先。'
                ],
                salary: '12-25万/年',
                salaryRange: [12, 25],
                path: ['轮岗学习', '定岗', '业务专家/团队经理', '部门/支行负责人'],
                companies: '招商银行、汇丰中国、工商银行'
            },
            {
                tier: '中后台',
                title: '合规与内控岗',
                jd: [
                    '负责数据安全、反洗钱、内部控制等相关合规工作。',
                    '制定和完善相关制度、流程，排查和防范合规风险。'
                ],
                requirements: [
                    '硕士及以上学历，法律、金融、经济、会计等专业。',
                    '逻辑严密，责任心强，具备优秀的文字和沟通能力。',
                    '熟悉金融监管政策和相关技术系统者优先。'
                ],
                salary: '总监级45-150万/年',
                salaryRange: [15, 35],
                path: ['合规专员', '高级合规经理', '合规总监', '风险管理/审计'],
                companies: '浦发银行、各大股份制银行'
            }
        ],
        '券商': [
            {
                tier: '前台',
                title: '投资银行分析师',
                jd: [
                    '参与IPO、并购重组、再融资等项目，进行尽职调查、材料撰写。',
                    '构建财务模型，进行企业估值和行业研究。',
                    '协助项目执行，与客户、中介机构沟通协调。'
                ],
                requirements: [
                    '硕士及以上学历，金融、会计、法律等专业，具备复合背景者优先。',
                    '持有CFA、法律职业资格证书者优先。',
                    '优秀的分析、建模、沟通能力和抗压能力。'
                ],
                salary: '25-60万/年',
                salaryRange: [25, 60],
                path: ['分析师(Analyst)', '经理(Associate)', '副总裁(VP)', '董事总经理(MD)'],
                companies: '中金公司、中信证券、华泰证券'
            },
            {
                tier: '中后台',
                title: '风险管理岗',
                jd: [
                    '负责市场风险、信用风险、操作风险的识别、计量、监控和报告。',
                    '参与量化交易等创新业务的风险控制。',
                    '完善公司全面风险管理体系，确保业务合规稳健运行。'
                ],
                requirements: [
                    '硕士及以上学历，金融、经济、统计、计算机等专业。',
                    '持有FRM、CFA证书者优先。',
                    '具备较强的数理分析和建模能力，熟悉相关风控工具。'
                ],
                salary: '20-50万/年',
                salaryRange: [20, 50],
                path: ['风控专员', '高级风控经理', '风控总监', '首席风险官(CRO)'],
                companies: '中信期货、国泰君安、海通证券'
            }
        ],
        '基金': [
            {
                tier: '前台',
                title: '投资经理',
                jd: [
                    '管理基金资产，制定并执行投资策略。',
                    '进行宏观、行业和个股研究，构建和调整投资组合。',
                    '负责路演、渠道沟通，向投资者阐述投资理念。'
                ],
                requirements: [
                    '硕士及以上学历，经济金融、理工科等复合背景优先。',
                    '通常由3-5年以上经验的研究员晋升，持有CFA者优先。',
                    '优秀的投研能力、决策能力和市场敏感度。'
                ],
                salary: '50-200万/年',
                salaryRange: [50, 200],
                path: ['研究员', '基金经理助理', '基金经理', '投研总监'],
                companies: '南方基金、华安基金、博道基金'
            },
            {
                tier: '中台',
                title: '基金研究员',
                jd: [
                    '深入研究宏观经济、行业趋势和上市公司。',
                    '撰写研究报告，为投资决策提供支持和建议。',
                    '进行上市公司调研，维护行业数据库。'
                ],
                requirements: [
                    '硕士及以上学历，经济金融、理工科等专业，对研究有浓厚兴趣。',
                    '通过CFA一级或二级者有显著优势。',
                    '具备扎实的研究分析能力、财务建模能力和报告撰写能力。'
                ],
                salary: '20-40万/年',
                salaryRange: [20, 40],
                path: ['初级研究员', '高级研究员', '资深研究员', '基金经理/投研总监'],
                companies: '易方达基金、嘉实基金、景顺长城'
            }
        ],
        '互联网金融': [
            {
                tier: '中台',
                title: '金融科技产品经理',
                jd: [
                    '负责金融产品的需求分析、功能设计和生命周期管理。',
                    '协调研发、设计、运营团队，推动产品上线和迭代。',
                    '研究市场和用户，结合业务目标进行产品创新。'
                ],
                requirements: [
                    '本科及以上学历，计算机、金融等复合背景优先。',
                    '熟悉互联网产品开发流程，具备用户思维和商业敏感度。',
                    '优秀的沟通协调能力和项目管理能力。'
                ],
                salary: '25-70万/年',
                salaryRange: [25, 70],
                path: ['产品专员', '产品经理', '高级产品经理', '产品总监'],
                companies: '蚂蚁集团、腾讯金融、微众银行'
            }
        ],
        '四大咨询': [
            {
                tier: '中台',
                title: '金融服务/风险咨询师',
                jd: [
                    '为银行、保险、券商等金融机构提供战略、运营、风控等咨询服务。',
                    '参与项目，进行行业研究、数据分析、方案设计和报告撰写。',
                    '协助客户应对监管要求，实施数字化转型。'
                ],
                requirements: [
                    '硕士及以上学历优先，专业不限，商科、理工科背景均可。',
                    '持有CFA、FRM、CPA等证书者优先。',
                    '顶尖的逻辑分析、解决问题和沟通表达能力。'
                ],
                salary: '15-40万/年',
                salaryRange: [15, 40],
                path: ['分析师', '咨询顾问', '高级顾问', '项目经理/合伙人'],
                companies: '普华永道、德勤、安永、毕马威'
            }
        ],
        'ESG': [
            {
                tier: '中台',
                title: 'ESG分析师',
                jd: [
                    '评估企业在环境、社会和公司治理方面的表现。',
                    '收集和分析ESG数据，构建评级模型，撰写分析报告。',
                    '为投资决策提供ESG视角，参与可持续金融产品设计。'
                ],
                requirements: [
                    '硕士及以上学历，环境、金融、经济等复合背景优先。',
                    '对可持续发展有深刻理解，具备优秀的研究分析能力。',
                    '持有CFA ESG证书者有加分。'
                ],
                salary: '25-60万/年',
                salaryRange: [25, 60],
                path: ['助理分析师', 'ESG分析师', '高级分析师', 'ESG研究/投资总监'],
                companies: '中金公司、商道融绿、MSCI'
            }
        ]
    },
    statistics: {
        '券商': [
            {
                tier: '中台',
                title: '量化研究员',
                jd: [
                    '利用数理统计方法和编程技术，挖掘市场规律，开发量化交易策略。',
                    '负责策略的回测、优化和实盘跟踪。',
                    '处理和分析海量金融数据，维护量化投研平台。'
                ],
                requirements: [
                    '硕士及以上学历，数学、统计、物理、计算机等顶尖理工科专业。',
                    '精通Python/C++，具备扎实的算法和数据结构基础。',
                '有量化实习或竞赛获奖经历者优先，通过CFA/FRM者优先。'
                ],
                salary: '30-100万+/年',
                salaryRange: [30, 100],
                path: ['初级量化研究员', '量化研究员', '资深研究员', '量化投资经理'],
                companies: '中信证券、华泰证券、幻方量化'
            },
            {
                tier: '中后台',
                title: '风险管理岗（量化方向）',
                jd: [
                    '负责量化策略的风险建模、压力测试和绩效归因。',
                    '监控交易系统和投资组合的风险暴露，设置风控参数。',
                    '开发和维护风险管理系统和工具。'
                ],
                requirements: [
                    '硕士及以上学历，金融工程、统计学、计算机等专业。',
                    '精通FRM知识体系，熟悉风险计量方法（如VaR）。',
                    '具备编程能力（Python/R），能处理和分析数据。'
                ],
                salary: '25-60万/年',
                salaryRange: [25, 60],
                path: ['风险专员', '量化风控经理', '高级经理', '风控总监'],
                companies: '国泰君安、海通证券、头部私募'
            }
        ],
        '基金': [
             {
                tier: '中台',
                title: '量化研究员',
                jd: [
                    '挖掘阿尔法/贝塔因子，开发股票、期货、期权等多品种量化策略。',
                    '协助数据清洗、整理、可视化以及数据库维护。',
                    '开发及维护量化策略绩效归因系统。'
                ],
                requirements: [
                    '顶尖院校硕士及以上学历，数学、统计、计算机等理工科专业。',
                    '优秀的编程能力（Python/C++）和数据处理能力。',
                    '在ACM、Kaggle、数学建模等竞赛中获奖者优先。'
                ],
                salary: '30-100万+/年',
                salaryRange: [30, 100],
                path: ['初级量化研究员', '量化研究员', '资深研究员', '量化投资经理'],
                companies: '博时基金、华夏基金、进化论资产'
            },
            {
                tier: '中后台',
                title: '风险监督员（量化）',
                jd: [
                    '隶属公司核心风控体系，对量化交易业务进行全周期实时监控。',
                    '核实基金仓位、持仓结构，及时识别并处置异常交易和风险事件。',
                ],
                requirements: [
                    '硕士及以上学历，数学、统计、计算机等专业。',
                    '具备编程能力，对金融市场和量化交易有一定理解。',
                    'FRM持证人或考生优先，要求高度的责任心和细致。'
                ],
                salary: '15-30万/年',
                salaryRange: [15, 30],
                path: ['风险监督员', '风控分析员', '策略审核岗', '运营管理'],
                companies: '博时基金、大型公募基金'
            }
        ],
        '商业银行': [
            {
                tier: '中后台',
                title: '数据分析师/数字金融岗',
                jd: [
                    '利用统计建模、数据挖掘等技术，分析客户行为、信贷风险、营销效果等。',
                    '搭建业务数据报表体系，为决策提供数据支持。',
                    '参与数字化产品的设计、营销和运营。'
                ],
                requirements: [
                    '本科及以上学历，统计、计算机、数学等专业。',
                    '熟练使用SQL、Python/R等数据分析工具。',
                    '具备数据敏感度和业务理解能力，善于从数据中发现问题。'
                ],
                salary: '15-40万/年',
                salaryRange: [15, 40],
                path: ['数据分析师', '高级数据分析师', '数据科学家', '数据部门负责人'],
                companies: '招商银行、平安银行、浦发银行'
            }
        ],
        '互联网金融': [
            {
                tier: '中台',
                title: '数据科学家/风控建模师',
                jd: [
                    '利用机器学习、深度学习等算法构建信用评分、反欺诈、营销响应等模型。',
                    '负责模型的开发、部署、监控和迭代优化。',
                    '深入分析海量数据，洞察业务机会和风险点。'
                ],
                requirements: [
                    '硕士及以上学历，统计、计算机、人工智能等顶尖专业。',
                    '扎实的机器学习理论基础和丰富的项目经验。',
                    '优秀的编程能力和大数据处理能力（如Spark/Hadoop）。'
                ],
                salary: '35-80万+/年',
                salaryRange: [35, 80],
                path: ['算法工程师', '数据科学家', '资深数据科学家', '首席科学家'],
                companies: '蚂蚁集团、微众银行、京东科技'
            }
        ],
        '四大咨询': [
            {
                tier: '中台',
                title: '数据与分析咨询顾问',
                jd: [
                    '为金融客户提供数据战略、数据治理、数据分析应用等咨询服务。',
                    '利用数据分析和可视化工具，帮助客户解决商业问题。',
                    '参与金融机构数字化转型项目，设计和实施数据驱动的解决方案。'
                ],
                requirements: [
                    '硕士及以上学历，统计、计算机、数学、商科等复合背景。',
                    '熟练使用SQL、Python/R、Tableau等分析工具。',
                    '优秀的商业理解能力、逻辑思维和沟通表达能力。'
                ],
                salary: '18-45万/年',
                salaryRange: [18, 45],
                path: ['分析师', '咨询顾问', '高级顾问', '项目经理/合伙人'],
                companies: '普华永道、德勤、安永、毕马威'
            }
        ]
    }
};

const collegeSelector = document.getElementById('college-selector');
const industryTagsContainer = document.getElementById('industry-tags');
const jobPostingsContainer = document.getElementById('job-postings');
let salaryChart = null;

let currentCollege = 'finance';
let currentIndustry = Object.keys(jobData.finance)[0];

function renderIndustryTags() {
    const industries = Object.keys(jobData[currentCollege]);
    industryTagsContainer.innerHTML = industries.map(industry => `
        <button class="industry-tag inline-block text-gray-700 font-medium py-2 px-4 rounded-full shadow-sm hover:bg-blue-100 transition duration-300" data-industry="${industry}">
            ${industry}
        </button>
    `).join('');
    
    // Add click listeners to new tags
    industryTagsContainer.querySelectorAll('.industry-tag').forEach(tag => {
        tag.addEventListener('click', () => {
            currentIndustry = tag.dataset.industry;
            updateActiveStates();
            renderJobPostings();
        });
    });
}

function renderJobPostings() {
    const postings = jobData[currentCollege][currentIndustry] || [];
    jobPostingsContainer.innerHTML = postings.map(job => `
        <div class="job-card glass-card rounded-xl overflow-hidden flex flex-col fade-in" data-job-title="${job.title}">
            <div class="p-5 flex-grow">
                <div class="flex justify-between items-start mb-2">
                    <h4 class="text-xl font-bold text-gray-800">${job.title}</h4>
                    <span class="text-xs font-semibold ${job.tier === '前台' ? 'bg-amber-100 text-amber-800' : 'bg-blue-100 text-blue-800'} py-1 px-2 rounded-full">${job.tier}</span>
                </div>
                <p class="text-blue-600 font-semibold mb-4">${job.companies}</p>
                
                <div class="mb-4">
                    <h5 class="font-bold text-gray-700 mb-2">岗位职责 (JD)</h5>
                    <ul class="list-disc list-inside space-y-1 text-sm text-gray-600">
                        ${job.jd.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                </div>

                <div class="mb-4">
                    <h5 class="font-bold text-gray-700 mb-2">能力要求</h5>
                    <ul class="list-disc list-inside space-y-1 text-sm text-gray-600">
                         ${job.requirements.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                </div>
                
                 <div class="mb-4">
                    <h5 class="font-bold text-gray-700 mb-2">发展路径</h5>
                     <div class="flex flex-wrap items-center text-sm text-gray-600">
                       ${job.path.map(step => `<span class="path-step font-medium">${step}</span>`).join('')}
                    </div>
                </div>

            </div>
            <div class="bg-gray-100 p-4 text-center">
                <span class="text-lg font-bold text-blue-700">${job.salary}</span>
            </div>
        </div>
    `).join('');
}


function renderSalaryChart() {
    const ctx = document.getElementById('salaryChart').getContext('2d');
    const collegeData = jobData[currentCollege];
    let chartData = [];

    for (const industry in collegeData) {
        collegeData[industry].forEach(job => {
            chartData.push({
                label: `${job.title} (${industry})`,
                data: job.salaryRange,
            });
        });
    }

    // Sort by starting salary
    chartData.sort((a, b) => a.data[0] - b.data[0]);

    if(salaryChart) {
        salaryChart.destroy();
    }
    
    salaryChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.map(d => d.label),
            datasets: [{
                label: '最低年薪 (万)',
                data: chartData.map(d => d.data[0]),
                backgroundColor: 'rgba(59, 130, 246, 0.7)', /* blue-500 with opacity */
                borderColor: 'rgba(37, 99, 235, 1)', /* blue-600 */
                borderWidth: 1,
                borderSkipped: false,
            },
            {
                label: '最高年薪 (万)',
                data: chartData.map(d => d.data[1] - d.data[0]),
                backgroundColor: 'rgba(96, 165, 250, 0.7)', /* blue-400 with opacity */
                borderColor: 'rgba(59, 130, 246, 1)', /* blue-500 */
                borderWidth: 1,
                borderSkipped: false,
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true,
                    title: {
                        display: true,
                        text: '年薪范围 (万元)',
                        font: { size: 14, weight: 'bold' }
                    }
                },
                y: {
                    stacked: true,
                    ticks: {
                        font: { size: 12 }
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const low = context.chart.data.datasets[0].data[context.dataIndex];
                            const high = context.chart.data.datasets[1].data[context.dataIndex] + low;
                            return `${context.chart.data.labels[context.dataIndex]}: ${low} - ${high}万`;
                        }
                    }
                }
            }
        }
    });
}


function updateActiveStates() {
    document.querySelectorAll('.nav-button').forEach(btn => btn.classList.remove('active'));
    document.getElementById(`btn-${currentCollege}`).classList.add('active');

    document.querySelectorAll('.industry-tag').forEach(tag => {
        if (tag.dataset.industry === currentIndustry) {
            tag.classList.add('active');
        } else {
            tag.classList.remove('active');
        }
    });
}

function initialize() {
    // Set up college selection buttons
    collegeSelector.addEventListener('click', (e) => {
        const button = e.target.closest('.nav-button');
        if (!button) return;

        if(button.id === 'btn-finance') {
            currentCollege = 'finance';
        } else {
            currentCollege = 'statistics';
        }
        
        currentIndustry = Object.keys(jobData[currentCollege])[0];
        
        renderIndustryTags();
        updateActiveStates();
        renderJobPostings();
        renderSalaryChart();
    });

    // Initial render
    renderIndustryTags();
    updateActiveStates();
    renderJobPostings();
    renderSalaryChart();

    // 设置职位卡片点击事件
    jobPostingsContainer.addEventListener('click', (e) => {
        const card = e.target.closest('.job-card');
        if (card) {
            const jobTitle = card.dataset.jobTitle;
            let job = null;
            
            // 查找对应的职位数据
            for (const industry in jobData[currentCollege]) {
                const foundJob = jobData[currentCollege][industry].find(j => j.title === jobTitle);
                if (foundJob) {
                    job = foundJob;
                    break;
                }
            }
            
            if (job) {
                openModal(job);
            }
        }
    });
}

// 模态框相关功能
const modal = document.getElementById('modal');
const modalContent = document.getElementById('modal-content');
const closeModalBtn = document.getElementById('close-modal');
let modalSalaryChart = null;

function openModal(job) {
    // 设置模态框内容
    document.getElementById('modal-title').textContent = job.title;
    document.getElementById('modal-jd').innerHTML = Array.isArray(job.jd) ? job.jd.map(item => `<li>${item}</li>`).join('') : `<li>${job.jd}</li>`;
    document.getElementById('modal-salary-text').textContent = `年薪范围: ${job.salary}`;
    document.getElementById('modal-companies').textContent = job.companies;

    // 设置技能标签
    const skillsContainer = document.getElementById('modal-skills');
    skillsContainer.innerHTML = '';
    job.requirements.forEach(skill => {
        const skillTag = document.createElement('span');
        skillTag.className = 'px-3 py-1 text-xs rounded-full bg-gray-200 text-gray-700 shadow-sm';
        skillTag.textContent = skill;
        skillsContainer.appendChild(skillTag);
    });

    // 设置发展路径
    const pathContainer = document.getElementById('modal-path');
    pathContainer.innerHTML = '';
    job.path.forEach((step, index) => {
        const stepTag = document.createElement('span');
        stepTag.className = 'path-step-item';
        stepTag.textContent = step;
        pathContainer.appendChild(stepTag);
        
        if (index < job.path.length - 1) {
            const arrow = document.createElement('span');
            arrow.className = 'path-arrow';
            arrow.textContent = '→';
            pathContainer.appendChild(arrow);
        }
    });

    // 显示模态框
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.classList.remove('opacity-0');
        modalContent.classList.remove('scale-95');
    }, 10);
    document.body.style.overflow = 'hidden';

    // 渲染薪资图表
    renderModalSalaryChart(job);
}

function renderModalSalaryChart(job) {
    const ctx = document.getElementById('modal-salary-chart').getContext('2d');
    
    if (modalSalaryChart) {
        modalSalaryChart.destroy();
    }
    
    modalSalaryChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['年薪范围 (万元)'],
            datasets: [{
                label: '最低年薪',
                data: [job.salaryRange[0]],
                backgroundColor: 'rgba(59, 130, 246, 0.6)', /* blue-500 with opacity */
                borderColor: 'rgba(59, 130, 246, 1)', /* blue-500 */
                borderWidth: 1,
                borderRadius: 5,
            }, {
                label: '最高年薪',
                data: [job.salaryRange[1] - job.salaryRange[0]],
                backgroundColor: 'rgba(37, 99, 235, 0.6)', /* blue-600 with opacity */
                borderColor: 'rgba(37, 99, 235, 1)', /* blue-600 */
                borderWidth: 1,
                borderRadius: 5,
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    beginAtZero: true,
                    stacked: true,
                    title: { display: true, text: '年薪 (万元)' }
                },
                y: {
                    stacked: true
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const low = context.chart.data.datasets[0].data[context.dataIndex];
                            const high = context.chart.data.datasets[1].data[context.dataIndex] + low;
                            return `${context.chart.data.labels[context.dataIndex]}: ${low} - ${high}万`;
                        }
                    }
                }
            }
        }
    });
}

function closeModal() {
    modal.classList.add('opacity-0');
    modalContent.classList.add('scale-95');
    setTimeout(() => {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }, 300);
}

// 添加关闭模态框的事件监听器
closeModalBtn.addEventListener('click', closeModal);
modal.addEventListener('click', (e) => {
    if (e.target === modal) {
        closeModal();
    }
});

document.addEventListener('DOMContentLoaded', initialize);
</script>

</body>
</html>
