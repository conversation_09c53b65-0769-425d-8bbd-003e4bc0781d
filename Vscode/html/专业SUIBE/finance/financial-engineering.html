<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金融工程专业深度解析 - 上海对外经贸大学</title>
    <meta name="description" content="上海对外经贸大学金融工程专业详细介绍，量化金融与风险管理专业人才培养">
    <meta name="keywords" content="上海对外经贸大学,金融工程,量化金融,风险管理,专业介绍">
    
    <!-- 外部资源 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 本地样式 -->
    <link rel="stylesheet" href="../assets/css/common.css">
    
    <style>
        .hero-bg {
            background: linear-gradient(135deg, rgba(227, 25, 55, 0.1) 0%, rgba(0, 0, 0, 0.9) 100%);
        }
        
        .major-highlight {
            background: linear-gradient(45deg, #1f2937 0%, #111827 100%);
            position: relative;
            overflow: hidden;
        }
        
        .major-highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23E31937" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 导航栏 -->
    <nav class="fixed top-0 left-0 right-0 bg-black/90 backdrop-blur-sm z-50 border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="../index.html" class="flex items-center space-x-2">
                        <i class="fas fa-university text-2xl tesla-red"></i>
                        <span class="text-xl font-bold">SUIBE</span>
                    </a>
                    <span class="text-gray-400">|</span>
                    <span class="text-gray-300">金融管理学院</span>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="../index.html" class="nav-link">返回首页</a>
                    <a href="#overview" class="nav-link">专业概览</a>
                    <a href="#courses" class="nav-link">课程体系</a>
                    <a href="#career" class="nav-link">职业发展</a>
                    <a href="#certifications" class="nav-link">相关认证</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="pt-20">
        <!-- 标题区域 -->
        <section class="hero-bg py-16 px-6">
            <div class="max-w-4xl mx-auto text-center">
                <div class="college-badge mb-4">金融管理学院</div>
                <h1 class="title-huge tesla-red mb-4 font-black">金融工程专业</h1>
                <h2 class="text-3xl md:text-4xl font-bold mb-3 text-gray-100">深度解析与发展指南</h2>
                <p class="text-medium-en text-gray-400 tracking-wider mb-8">FINANCIAL ENGINEERING MAJOR COMPREHENSIVE GUIDE</p>
                <div class="w-32 h-1 bg-tesla-red mx-auto mb-8 rounded-full"></div>
                <p class="text-lg text-gray-300 leading-relaxed max-w-2xl mx-auto">
                    培养掌握现代金融理论和数量分析方法的复合型量化金融人才
                </p>
            </div>
        </section>

        <!-- 专业核心优势 -->
        <section id="overview" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <div class="grid-bento mb-12">
                    <!-- 专业核心优势 - 大卡片 -->
                    <div class="span-8 major-highlight rounded-2xl p-8 card-hover border border-gray-800 line-graphic">
                        <div class="flex items-center mb-6 relative z-10">
                            <i class="fas fa-star text-3xl tesla-red mr-4"></i>
                            <h3 class="text-3xl font-bold">核心竞争优势</h3>
                        </div>
                        <div class="grid grid-cols-2 gap-6 relative z-10">
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">数理融合</h4>
                                <p class="text-sm">数学、统计学、计算机科学与金融学交叉融合，培养量化分析能力</p>
                                <p class="text-small-en mt-2">MATHEMATICAL INTEGRATION</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">量化建模</h4>
                                <p class="text-sm">强化量化分析和金融建模能力，掌握现代金融工程技术</p>
                                <p class="text-small-en mt-2">QUANTITATIVE MODELING</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">科技应用</h4>
                                <p class="text-sm">金融科技应用导向，培养金融创新和技术应用能力</p>
                                <p class="text-small-en mt-2">FINTECH APPLICATION</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">衍生品专长</h4>
                                <p class="text-sm">衍生品定价和风险管理专业优势，掌握复杂金融工具</p>
                                <p class="text-small-en mt-2">DERIVATIVES EXPERTISE</p>
                            </div>
                        </div>
                    </div>

                    <!-- 专业数据 - 小卡片 -->
                    <div class="span-4 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <div class="text-center">
                            <i class="fas fa-calculator text-4xl tesla-red mb-4"></i>
                            <div class="space-y-4">
                                <div>
                                    <div class="text-2xl font-bold tesla-red">020302</div>
                                    <p class="text-sm text-gray-400">专业代码</p>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold tesla-red">4年</div>
                                    <p class="text-sm text-gray-400">学制</p>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-gray-100">经济学学士</div>
                                    <p class="text-sm text-gray-400">学位</p>
                                </div>
                            </div>
                            <p class="text-small-en mt-4">BACHELOR OF ECONOMICS</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 职业发展路径 -->
        <section id="career" class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">
                        <i class="fas fa-route tesla-red mr-4"></i>
                        职业发展路径
                    </h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">
                        金融工程专业毕业生在量化投资、风险管理、金融科技等领域具有独特优势
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">量化投资</h3>
                                <p class="text-small-en opacity-80">QUANTITATIVE INVESTMENT</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">运用数学模型和计算机技术进行投资决策</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>量化分析师：开发投资策略和交易模型</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>算法交易员：执行程序化交易策略</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>策略研究员：研究市场规律和投资机会</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">风险管理</h3>
                                <p class="text-small-en opacity-80">RISK MANAGEMENT</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">运用量化方法识别、测量和控制金融风险</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>风险建模师：构建风险测量和预警模型</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>压力测试专员：设计和执行压力测试方案</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>合规分析师：确保业务符合监管要求</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">金融科技</h3>
                                <p class="text-small-en opacity-80">FINTECH</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">运用技术手段创新金融服务和产品</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>金融产品经理：设计和管理金融科技产品</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>数据科学家：挖掘金融数据价值</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>系统分析师：设计金融信息系统</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">投资银行</h3>
                                <p class="text-small-en opacity-80">INVESTMENT BANKING</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">为企业和机构提供专业的投融资服务</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>结构化产品设计师：设计复杂金融产品</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>衍生品交易员：交易期货期权等衍生品</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>定价分析师：为金融产品进行估值定价</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">资产管理</h3>
                                <p class="text-small-en opacity-80">ASSET MANAGEMENT</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">为客户提供专业的资产配置和投资管理服务</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>投资组合经理：管理客户投资组合</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>研究分析师：分析投资标的和市场</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>量化策略师：开发量化投资策略</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 核心课程体系 -->
        <section id="courses" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">
                        <i class="fas fa-book tesla-red mr-4"></i>
                        核心课程体系
                    </h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">
                        融合数学、统计、计算机和金融的跨学科课程设置
                    </p>
                </div>

                <div class="grid-bento">
                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">数理基础</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">高等数学</p>
                                <p class="text-small-en mt-1">Advanced Mathematics</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">线性代数</p>
                                <p class="text-small-en mt-1">Linear Algebra</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">概率论与数理统计</p>
                                <p class="text-small-en mt-1">Probability & Statistics</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">随机过程</p>
                                <p class="text-small-en mt-1">Stochastic Process</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">金融理论</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">金融学原理</p>
                                <p class="text-small-en mt-1">Principles of Finance</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">投资学</p>
                                <p class="text-small-en mt-1">Investment</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">公司金融</p>
                                <p class="text-small-en mt-1">Corporate Finance</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">国际金融</p>
                                <p class="text-small-en mt-1">International Finance</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">工程技术</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">金融工程学</p>
                                <p class="text-small-en mt-1">Financial Engineering</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">衍生金融工具</p>
                                <p class="text-small-en mt-1">Derivatives</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">固定收益证券</p>
                                <p class="text-small-en mt-1">Fixed Income Securities</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">金融风险管理</p>
                                <p class="text-small-en mt-1">Risk Management</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">编程应用</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">Python金融编程</p>
                                <p class="text-small-en mt-1">Python for Finance</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">R语言统计分析</p>
                                <p class="text-small-en mt-1">R Statistical Analysis</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">MATLAB金融建模</p>
                                <p class="text-small-en mt-1">MATLAB Modeling</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数据库管理</p>
                                <p class="text-small-en mt-1">Database Management</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 课程体系详解 -->
        <section class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">专业课程体系深度解析</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-square-root-alt mr-2"></i>
                            数理基础模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">高等数学</p>
                                <p class="text-gray-400 text-xs">微积分、级数理论基础</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">线性代数</p>
                                <p class="text-gray-400 text-xs">矩阵运算与投资组合优化</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">随机过程</p>
                                <p class="text-gray-400 text-xs">金融建模的核心数学工具</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-chart-line mr-2"></i>
                            金融理论模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">现代投资组合理论</p>
                                <p class="text-gray-400 text-xs">Markowitz模型与CAPM</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">期权定价理论</p>
                                <p class="text-gray-400 text-xs">Black-Scholes模型</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">利率期限结构</p>
                                <p class="text-gray-400 text-xs">债券定价与收益率曲线</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-cogs mr-2"></i>
                            工程技术模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">金融工程学</p>
                                <p class="text-gray-400 text-xs">金融产品设计与创新</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">衍生品定价</p>
                                <p class="text-gray-400 text-xs">期货期权等衍生工具</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">结构化产品</p>
                                <p class="text-gray-400 text-xs">复杂金融产品设计</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-shield-alt mr-2"></i>
                            风险管理模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">金融风险管理</p>
                                <p class="text-gray-400 text-xs">VaR模型与风险度量</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">信用风险建模</p>
                                <p class="text-gray-400 text-xs">违约概率与损失评估</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">操作风险管理</p>
                                <p class="text-gray-400 text-xs">内部控制与合规管理</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-code mr-2"></i>
                            编程技术模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">Python金融编程</p>
                                <p class="text-gray-400 text-xs">数据分析与算法实现</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">R语言统计分析</p>
                                <p class="text-gray-400 text-xs">统计建模与数据挖掘</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">MATLAB金融建模</p>
                                <p class="text-gray-400 text-xs">数值计算与仿真</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-database mr-2"></i>
                            数据技术模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">数据库管理</p>
                                <p class="text-gray-400 text-xs">SQL与金融数据处理</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">机器学习应用</p>
                                <p class="text-gray-400 text-xs">AI在金融中的应用</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">大数据分析</p>
                                <p class="text-gray-400 text-xs">海量金融数据处理</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 相关认证简介 -->
        <section id="certifications" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">相关认证简介</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-shield-alt text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">FRM</h4>
                            <p class="text-sm text-gray-400 mb-3">金融风险管理师</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">全球金融风险管理领域权威认证，与金融工程专业的风险建模基础高度契合</p>
                    </div>

                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-calculator text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">CQF</h4>
                            <p class="text-sm text-gray-400 mb-3">量化金融分析师</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">专业量化金融认证，专注于数学建模和量化分析技能，是量化金融专业人士的首选认证</p>
                    </div>

                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-award text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">CFA</h4>
                            <p class="text-sm text-gray-400 mb-3">特许金融分析师</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">全球投资分析领域最权威认证，金融工程专业的量化分析基础为CFA考试提供强有力支撑</p>
                    </div>
                </div>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-400 leading-relaxed">
                        以上认证可根据个人职业发展方向选择性考虑，建议结合专业学习进度和实习经验合理规划
                    </p>
                </div>
            </div>
        </section>

        <!-- 专业发展建议 -->
        <section class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">专业发展核心建议</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">01</div>
                        <h4 class="text-xl font-bold mb-4">强化数理基础</h4>
                        <p class="text-sm mb-4">深入掌握数学、统计学和计算机科学基础，为量化分析奠定坚实基础</p>
                        <div class="text-xs text-gray-400">
                            <p>• 精通高等数学和随机过程</p>
                            <p>• 掌握统计建模方法</p>
                            <p>• 建立数理思维模式</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">02</div>
                        <h4 class="text-xl font-bold mb-4">提升编程能力</h4>
                        <p class="text-sm mb-4">熟练掌握金融编程语言和工具，具备独立开发金融模型的能力</p>
                        <div class="text-xs text-gray-400">
                            <p>• 精通Python、R、MATLAB</p>
                            <p>• 掌握数据库操作技能</p>
                            <p>• 学习机器学习算法</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">03</div>
                        <h4 class="text-xl font-bold mb-4">关注金融科技发展</h4>
                        <p class="text-sm mb-4">紧跟金融科技发展趋势，培养创新思维和技术应用能力</p>
                        <div class="text-xs text-gray-400">
                            <p>• 了解区块链、AI等新技术</p>
                            <p>• 关注金融科技应用场景</p>
                            <p>• 培养跨界融合思维</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 总结与展望 -->
        <section class="py-16 px-6">
            <div class="max-w-4xl mx-auto text-center bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-12 border border-gray-700">
                <h3 class="text-4xl font-bold mb-6">
                    <span class="tesla-red">量化未来</span> · 工程创新
                </h3>
                <p class="text-xl mb-8 leading-relaxed">
                    金融工程专业凭借其独特的数理基础与金融应用相结合的特色，
                    为学生在量化金融和金融科技领域的发展奠定了坚实基础。
                    从传统风险管理到金融科技创新，从衍生品定价到算法交易，
                    金融工程毕业生正在用数理智慧重新定义金融行业的技术边界。
                </p>
                <div class="flex justify-center items-center space-x-8 text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-graduation-cap tesla-red mr-2"></i>
                        <span>专业深度</span>
                    </div>
                    <div class="w-2 h-2 bg-tesla-red rounded-full"></div>
                    <div class="flex items-center">
                        <i class="fas fa-globe tesla-red mr-2"></i>
                        <span>国际视野</span>
                    </div>
                    <div class="w-2 h-2 bg-tesla-red rounded-full"></div>
                    <div class="flex items-center">
                        <i class="fas fa-rocket tesla-red mr-2"></i>
                        <span>创新应用</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="bg-gray-900 py-8 px-6 border-t border-gray-800">
            <div class="max-w-7xl mx-auto text-center">
                <div class="text-sm text-gray-500">
                    <p>&copy; 上海对外经贸大学金融管理学院 | 金融工程专业介绍</p>
                    <p class="mt-2">Shanghai University of International Business and Economics - Financial Engineering Major</p>
                </div>
            </div>
        </footer>
    </main>

    <!-- 脚本文件 -->
    <script src="../assets/js/common.js"></script>
</body>
</html>
