<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人工智能专业深度解析 - 上海对外经贸大学</title>
    <meta name="description" content="上海对外经贸大学人工智能专业详细介绍，AI算法与智能系统开发专业人才培养">
    <meta name="keywords" content="上海对外经贸大学,人工智能,AI算法,智能系统,专业介绍">
    
    <!-- 外部资源 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 本地样式 -->
    <link rel="stylesheet" href="../assets/css/common.css">
    
    <style>
        .hero-bg {
            background: linear-gradient(135deg, rgba(227, 25, 55, 0.1) 0%, rgba(0, 0, 0, 0.9) 100%);
        }
        
        .major-highlight {
            background: linear-gradient(45deg, #1f2937 0%, #111827 100%);
            position: relative;
            overflow: hidden;
        }
        
        .major-highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23E31937" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 导航栏 -->
    <nav class="fixed top-0 left-0 right-0 bg-black/90 backdrop-blur-sm z-50 border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="../index.html" class="flex items-center space-x-2">
                        <i class="fas fa-university text-2xl tesla-red"></i>
                        <span class="text-xl font-bold">SUIBE</span>
                    </a>
                    <span class="text-gray-400">|</span>
                    <span class="text-gray-300">统计与信息学院</span>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="../index.html" class="nav-link">返回首页</a>
                    <a href="#overview" class="nav-link">专业概览</a>
                    <a href="#courses" class="nav-link">课程体系</a>
                    <a href="#career" class="nav-link">职业发展</a>
                    <a href="#certifications" class="nav-link">相关认证</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="pt-20">
        <!-- 标题区域 -->
        <section class="hero-bg py-16 px-6">
            <div class="max-w-4xl mx-auto text-center">
                <div class="college-badge mb-4">统计与信息学院</div>
                <h1 class="title-huge tesla-red mb-4 font-black">人工智能专业</h1>
                <h2 class="text-3xl md:text-4xl font-bold mb-3 text-gray-100">深度解析与发展指南</h2>
                <p class="text-medium-en text-gray-400 tracking-wider mb-8">ARTIFICIAL INTELLIGENCE MAJOR COMPREHENSIVE GUIDE</p>
                <div class="w-32 h-1 bg-tesla-red mx-auto mb-8 rounded-full"></div>
                <p class="text-lg text-gray-300 leading-relaxed max-w-2xl mx-auto">
                    培养具备AI算法设计和智能系统开发能力的前沿技术人才
                </p>
            </div>
        </section>

        <!-- 专业核心优势 -->
        <section id="overview" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <div class="grid-bento mb-12">
                    <!-- 专业核心优势 - 大卡片 -->
                    <div class="span-8 major-highlight rounded-2xl p-8 card-hover border border-gray-800 line-graphic">
                        <div class="flex items-center mb-6 relative z-10">
                            <i class="fas fa-star text-3xl tesla-red mr-4"></i>
                            <h3 class="text-3xl font-bold">核心竞争优势</h3>
                        </div>
                        <div class="grid grid-cols-2 gap-6 relative z-10">
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">算法创新能力</h4>
                                <p class="text-sm">深度学习、机器学习算法设计与优化，培养AI核心技术能力</p>
                                <p class="text-small-en mt-2">ALGORITHM INNOVATION</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">智能系统开发</h4>
                                <p class="text-sm">端到端AI系统设计开发，从模型训练到产品部署</p>
                                <p class="text-small-en mt-2">INTELLIGENT SYSTEM DEVELOPMENT</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">跨领域应用</h4>
                                <p class="text-sm">AI在金融、医疗、教育、制造等多领域的应用实践</p>
                                <p class="text-small-en mt-2">CROSS-DOMAIN APPLICATION</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">前沿技术跟踪</h4>
                                <p class="text-sm">紧跟AI前沿发展，掌握最新技术趋势和研究方向</p>
                                <p class="text-small-en mt-2">CUTTING-EDGE TECHNOLOGY</p>
                            </div>
                        </div>
                    </div>

                    <!-- 专业数据 - 小卡片 -->
                    <div class="span-4 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <div class="text-center">
                            <i class="fas fa-brain text-4xl tesla-red mb-4"></i>
                            <div class="space-y-4">
                                <div>
                                    <div class="text-2xl font-bold tesla-red">080717T</div>
                                    <p class="text-sm text-gray-400">专业代码</p>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold tesla-red">4年</div>
                                    <p class="text-sm text-gray-400">学制</p>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-gray-100">工学学士</div>
                                    <p class="text-sm text-gray-400">学位</p>
                                </div>
                            </div>
                            <p class="text-small-en mt-4">BACHELOR OF ENGINEERING</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 职业发展路径 -->
        <section id="career" class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">
                        <i class="fas fa-route tesla-red mr-4"></i>
                        职业发展路径
                    </h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">
                        人工智能专业毕业生在科技公司、研究院所、创业公司等领域具有广阔发展前景
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">科技巨头</h3>
                                <p class="text-small-en opacity-80">TECH GIANTS</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">Google、微软、百度、阿里等科技公司</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>AI算法工程师：设计和优化机器学习算法</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>深度学习工程师：开发神经网络模型</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>AI产品经理：规划AI产品发展方向</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">AI独角兽</h3>
                                <p class="text-small-en opacity-80">AI UNICORNS</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">商汤、旷视、依图等AI专业公司</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>计算机视觉工程师：开发图像识别系统</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>自然语言处理工程师：构建NLP应用</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>AI研究员：从事前沿AI技术研究</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">研究院所</h3>
                                <p class="text-small-en opacity-80">RESEARCH INSTITUTES</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">中科院、清华AIR等科研机构</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>科研助理：参与AI前沿技术研究</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>博士研究生：深入AI理论研究</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>项目工程师：承担科研项目开发</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">传统行业+AI</h3>
                                <p class="text-small-en opacity-80">TRADITIONAL INDUSTRY + AI</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">金融、医疗、制造、教育等行业</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>AI解决方案工程师：为行业提供AI解决方案</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>智能化项目经理：推进行业智能化升级</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>AI顾问：为企业提供AI战略咨询</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">AI创业</h3>
                                <p class="text-small-en opacity-80">AI STARTUPS</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">AI技术创业和产品创新</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>技术创始人：创立AI技术公司</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>CTO：担任技术负责人</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>AI产品总监：负责AI产品规划</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 核心课程体系 -->
        <section id="courses" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">
                        <i class="fas fa-book tesla-red mr-4"></i>
                        核心课程体系
                    </h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">
                        理论基础与实践应用并重的AI专业课程设置
                    </p>
                </div>

                <div class="grid-bento">
                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">数学基础</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">高等数学</p>
                                <p class="text-small-en mt-1">Advanced Mathematics</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">线性代数</p>
                                <p class="text-small-en mt-1">Linear Algebra</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">概率论与数理统计</p>
                                <p class="text-small-en mt-1">Probability & Statistics</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">最优化理论</p>
                                <p class="text-small-en mt-1">Optimization Theory</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">计算机基础</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">程序设计基础</p>
                                <p class="text-small-en mt-1">Programming Fundamentals</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数据结构与算法</p>
                                <p class="text-small-en mt-1">Data Structures & Algorithms</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">计算机系统</p>
                                <p class="text-small-en mt-1">Computer Systems</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数据库系统</p>
                                <p class="text-small-en mt-1">Database Systems</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">AI核心</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">机器学习</p>
                                <p class="text-small-en mt-1">Machine Learning</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">深度学习</p>
                                <p class="text-small-en mt-1">Deep Learning</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">神经网络</p>
                                <p class="text-small-en mt-1">Neural Networks</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">强化学习</p>
                                <p class="text-small-en mt-1">Reinforcement Learning</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">应用领域</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">计算机视觉</p>
                                <p class="text-small-en mt-1">Computer Vision</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">自然语言处理</p>
                                <p class="text-small-en mt-1">Natural Language Processing</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">语音识别</p>
                                <p class="text-small-en mt-1">Speech Recognition</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">智能机器人</p>
                                <p class="text-small-en mt-1">Intelligent Robotics</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 课程体系详解 -->
        <section class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">专业课程体系深度解析</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-brain mr-2"></i>
                            机器学习模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">监督学习</p>
                                <p class="text-gray-400 text-xs">分类与回归算法</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">无监督学习</p>
                                <p class="text-gray-400 text-xs">聚类与降维算法</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">强化学习</p>
                                <p class="text-gray-400 text-xs">智能决策与控制</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-network-wired mr-2"></i>
                            深度学习模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">卷积神经网络</p>
                                <p class="text-gray-400 text-xs">CNN图像处理</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">循环神经网络</p>
                                <p class="text-gray-400 text-xs">RNN序列建模</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">Transformer架构</p>
                                <p class="text-gray-400 text-xs">注意力机制模型</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-eye mr-2"></i>
                            计算机视觉模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">图像识别</p>
                                <p class="text-gray-400 text-xs">物体检测与分类</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">图像分割</p>
                                <p class="text-gray-400 text-xs">语义分割技术</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">目标跟踪</p>
                                <p class="text-gray-400 text-xs">视频目标跟踪</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-comments mr-2"></i>
                            自然语言处理模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">文本分析</p>
                                <p class="text-gray-400 text-xs">情感分析与分类</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">机器翻译</p>
                                <p class="text-gray-400 text-xs">神经机器翻译</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">对话系统</p>
                                <p class="text-gray-400 text-xs">聊天机器人开发</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-microphone mr-2"></i>
                            语音技术模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">语音识别</p>
                                <p class="text-gray-400 text-xs">ASR自动语音识别</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">语音合成</p>
                                <p class="text-gray-400 text-xs">TTS文本转语音</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">声纹识别</p>
                                <p class="text-gray-400 text-xs">说话人识别技术</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-cogs mr-2"></i>
                            工程实践模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">模型部署</p>
                                <p class="text-gray-400 text-xs">生产环境部署</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">模型优化</p>
                                <p class="text-gray-400 text-xs">性能调优与压缩</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">MLOps</p>
                                <p class="text-gray-400 text-xs">机器学习运维</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 相关认证简介 -->
        <section id="certifications" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">相关认证简介</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-certificate text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">TensorFlow认证</h4>
                            <p class="text-sm text-gray-400 mb-3">Google官方认证</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">Google TensorFlow开发者认证，验证深度学习和机器学习技能</p>
                    </div>

                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-cloud text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">AWS ML认证</h4>
                            <p class="text-sm text-gray-400 mb-3">云端机器学习认证</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">亚马逊云服务机器学习专业认证，涵盖云端AI服务和部署</p>
                    </div>

                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-award text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">CFA</h4>
                            <p class="text-sm text-gray-400 mb-3">特许金融分析师</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">全球投资分析领域权威认证，AI算法与金融建模结合，培养复合型智能金融科技人才</p>
                    </div>
                </div>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-400 leading-relaxed">
                        以上认证可根据个人职业发展方向选择性考虑，建议结合专业学习进度和实习经验合理规划
                    </p>
                </div>
            </div>
        </section>

        <!-- 专业发展建议 -->
        <section class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">专业发展核心建议</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">01</div>
                        <h4 class="text-xl font-bold mb-4">夯实数理基础</h4>
                        <p class="text-sm mb-4">深入掌握数学、统计学基础，为AI算法理解奠定坚实基础</p>
                        <div class="text-xs text-gray-400">
                            <p>• 精通线性代数和概率论</p>
                            <p>• 掌握最优化理论</p>
                            <p>• 理解统计学习理论</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">02</div>
                        <h4 class="text-xl font-bold mb-4">强化编程实践</h4>
                        <p class="text-sm mb-4">熟练掌握AI开发工具和框架，具备端到端项目开发能力</p>
                        <div class="text-xs text-gray-400">
                            <p>• 精通Python、PyTorch、TensorFlow</p>
                            <p>• 掌握云计算平台使用</p>
                            <p>• 学习模型部署和优化</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">03</div>
                        <h4 class="text-xl font-bold mb-4">跟踪前沿发展</h4>
                        <p class="text-sm mb-4">密切关注AI前沿技术发展，培养持续学习和创新能力</p>
                        <div class="text-xs text-gray-400">
                            <p>• 关注顶级会议论文</p>
                            <p>• 参与开源项目贡献</p>
                            <p>• 培养跨领域应用思维</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 总结与展望 -->
        <section class="py-16 px-6">
            <div class="max-w-4xl mx-auto text-center bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-12 border border-gray-700">
                <h3 class="text-4xl font-bold mb-6">
                    <span class="tesla-red">智能创造</span> · 未来已来
                </h3>
                <p class="text-xl mb-8 leading-relaxed">
                    人工智能专业凭借其前沿的技术内容和广阔的应用前景，
                    为学生在AI时代的发展奠定了坚实基础。
                    从算法创新到系统开发，从学术研究到产业应用，
                    人工智能专业毕业生正在用智能技术重塑世界，创造未来。
                </p>
                <div class="flex justify-center items-center space-x-8 text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-graduation-cap tesla-red mr-2"></i>
                        <span>专业深度</span>
                    </div>
                    <div class="w-2 h-2 bg-tesla-red rounded-full"></div>
                    <div class="flex items-center">
                        <i class="fas fa-globe tesla-red mr-2"></i>
                        <span>国际视野</span>
                    </div>
                    <div class="w-2 h-2 bg-tesla-red rounded-full"></div>
                    <div class="flex items-center">
                        <i class="fas fa-rocket tesla-red mr-2"></i>
                        <span>创新应用</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="bg-gray-900 py-8 px-6 border-t border-gray-800">
            <div class="max-w-7xl mx-auto text-center">
                <div class="text-sm text-gray-500">
                    <p>&copy; 上海对外经贸大学统计与信息学院 | 人工智能专业介绍</p>
                    <p class="mt-2">Shanghai University of International Business and Economics - Artificial Intelligence Major</p>
                </div>
            </div>
        </footer>
    </main>

    <!-- 脚本文件 -->
    <script src="../assets/js/common.js"></script>
</body>
</html>
