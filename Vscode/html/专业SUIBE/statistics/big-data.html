<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大数据管理与应用专业深度解析 - 上海对外经贸大学</title>
    <meta name="description" content="上海对外经贸大学大数据管理与应用专业详细介绍，大数据技术与商业应用专业人才培养">
    <meta name="keywords" content="上海对外经贸大学,大数据管理与应用,大数据技术,商业应用,专业介绍">
    
    <!-- 外部资源 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 本地样式 -->
    <link rel="stylesheet" href="../assets/css/common.css">
    
    <style>
        .hero-bg {
            background: linear-gradient(135deg, rgba(227, 25, 55, 0.1) 0%, rgba(0, 0, 0, 0.9) 100%);
        }
        
        .major-highlight {
            background: linear-gradient(45deg, #1f2937 0%, #111827 100%);
            position: relative;
            overflow: hidden;
        }
        
        .major-highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23E31937" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- 导航栏 -->
    <nav class="fixed top-0 left-0 right-0 bg-black/90 backdrop-blur-sm z-50 border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="../index.html" class="flex items-center space-x-2">
                        <i class="fas fa-university text-2xl tesla-red"></i>
                        <span class="text-xl font-bold">SUIBE</span>
                    </a>
                    <span class="text-gray-400">|</span>
                    <span class="text-gray-300">统计与信息学院</span>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="../index.html" class="nav-link">返回首页</a>
                    <a href="#overview" class="nav-link">专业概览</a>
                    <a href="#courses" class="nav-link">课程体系</a>
                    <a href="#career" class="nav-link">职业发展</a>
                    <a href="#certifications" class="nav-link">相关认证</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="pt-20">
        <!-- 标题区域 -->
        <section class="hero-bg py-16 px-6">
            <div class="max-w-4xl mx-auto text-center">
                <div class="college-badge mb-4">统计与信息学院</div>
                <h1 class="title-huge tesla-red mb-4 font-black">大数据管理与应用专业</h1>
                <h2 class="text-3xl md:text-4xl font-bold mb-3 text-gray-100">深度解析与发展指南</h2>
                <p class="text-medium-en text-gray-400 tracking-wider mb-8">BIG DATA MANAGEMENT & APPLICATION COMPREHENSIVE GUIDE</p>
                <div class="w-32 h-1 bg-tesla-red mx-auto mb-8 rounded-full"></div>
                <p class="text-lg text-gray-300 leading-relaxed max-w-2xl mx-auto">
                    培养具备大数据技术和商业应用能力的复合型人才
                </p>
            </div>
        </section>

        <!-- 专业核心优势 -->
        <section id="overview" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <div class="grid-bento mb-12">
                    <!-- 专业核心优势 - 大卡片 -->
                    <div class="span-8 major-highlight rounded-2xl p-8 card-hover border border-gray-800 line-graphic">
                        <div class="flex items-center mb-6 relative z-10">
                            <i class="fas fa-star text-3xl tesla-red mr-4"></i>
                            <h3 class="text-3xl font-bold">核心竞争优势</h3>
                        </div>
                        <div class="grid grid-cols-2 gap-6 relative z-10">
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">技术与商业融合</h4>
                                <p class="text-sm">大数据技术与商业应用深度融合，培养技术+商业双重能力</p>
                                <p class="text-small-en mt-2">TECH-BUSINESS INTEGRATION</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">全栈数据能力</h4>
                                <p class="text-sm">从数据采集、存储、处理到分析应用的全栈数据技能</p>
                                <p class="text-small-en mt-2">FULL-STACK DATA SKILLS</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">云计算平台</h4>
                                <p class="text-sm">掌握主流云计算平台和大数据生态系统技术</p>
                                <p class="text-small-en mt-2">CLOUD COMPUTING</p>
                            </div>
                            <div class="bg-tesla-red-dark rounded-xl p-6 border border-tesla-red">
                                <h4 class="text-xl font-bold mb-3 tesla-red">行业应用导向</h4>
                                <p class="text-sm">面向金融、电商、制造等行业的大数据应用实践</p>
                                <p class="text-small-en mt-2">INDUSTRY APPLICATION</p>
                            </div>
                        </div>
                    </div>

                    <!-- 专业数据 - 小卡片 -->
                    <div class="span-4 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <div class="text-center">
                            <i class="fas fa-database text-4xl tesla-red mb-4"></i>
                            <div class="space-y-4">
                                <div>
                                    <div class="text-2xl font-bold tesla-red">120108T</div>
                                    <p class="text-sm text-gray-400">专业代码</p>
                                </div>
                                <div>
                                    <div class="text-2xl font-bold tesla-red">4年</div>
                                    <p class="text-sm text-gray-400">学制</p>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-gray-100">管理学学士</div>
                                    <p class="text-sm text-gray-400">学位</p>
                                </div>
                            </div>
                            <p class="text-small-en mt-4">BACHELOR OF MANAGEMENT</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 职业发展路径 -->
        <section id="career" class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">
                        <i class="fas fa-route tesla-red mr-4"></i>
                        职业发展路径
                    </h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">
                        大数据管理与应用专业毕业生在互联网、金融科技、咨询等领域具有广阔发展前景
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">互联网大厂</h3>
                                <p class="text-small-en opacity-80">INTERNET GIANTS</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">阿里、腾讯、字节跳动等互联网公司</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>大数据工程师：构建大数据处理平台</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>数据产品经理：设计数据驱动的产品</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>数据科学家：挖掘数据商业价值</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">金融科技</h3>
                                <p class="text-small-en opacity-80">FINTECH COMPANIES</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">蚂蚁金服、京东数科等金融科技公司</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>风控数据分析师：构建风险控制模型</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>量化分析师：开发量化交易策略</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>金融数据工程师：处理金融大数据</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">咨询公司</h3>
                                <p class="text-small-en opacity-80">CONSULTING FIRMS</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">麦肯锡、德勤等管理咨询公司</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>数据咨询顾问：为企业提供数据战略咨询</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>商业分析师：分析商业数据，支持决策</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>数字化转型顾问：帮助企业数字化升级</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">传统企业</h3>
                                <p class="text-small-en opacity-80">TRADITIONAL ENTERPRISES</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">制造业、零售业等传统行业企业</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>数据分析经理：建设企业数据分析体系</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>商业智能专员：开发BI系统和报表</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>数字化项目经理：推进企业数字化项目</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-xl p-6 card-hover border border-gray-800">
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-tesla-red rounded-full mr-4"></div>
                            <div>
                                <h3 class="text-lg font-bold">创业公司</h3>
                                <p class="text-small-en opacity-80">STARTUPS</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3">大数据和人工智能创业公司</p>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>技术合伙人：参与技术创业项目</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>全栈数据工程师：负责数据技术全栈开发</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-circle text-tesla-red mr-2" style="font-size: 4px;"></i>
                                <span>产品技术经理：管理数据产品开发</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 核心课程体系 -->
        <section id="courses" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">
                        <i class="fas fa-book tesla-red mr-4"></i>
                        核心课程体系
                    </h2>
                    <p class="text-gray-400 max-w-2xl mx-auto">
                        技术基础与商业应用并重的大数据课程设置
                    </p>
                </div>

                <div class="grid-bento">
                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">技术基础</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">程序设计基础</p>
                                <p class="text-small-en mt-1">Programming Fundamentals</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数据结构与算法</p>
                                <p class="text-small-en mt-1">Data Structures & Algorithms</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数据库原理</p>
                                <p class="text-small-en mt-1">Database Principles</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">操作系统</p>
                                <p class="text-small-en mt-1">Operating Systems</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">大数据技术</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">Hadoop生态系统</p>
                                <p class="text-small-en mt-1">Hadoop Ecosystem</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">Spark计算框架</p>
                                <p class="text-small-en mt-1">Apache Spark</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">NoSQL数据库</p>
                                <p class="text-small-en mt-1">NoSQL Databases</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">流式计算</p>
                                <p class="text-small-en mt-1">Stream Processing</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">数据分析</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">统计学基础</p>
                                <p class="text-small-en mt-1">Statistics Fundamentals</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">机器学习</p>
                                <p class="text-small-en mt-1">Machine Learning</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数据挖掘</p>
                                <p class="text-small-en mt-1">Data Mining</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数据可视化</p>
                                <p class="text-small-en mt-1">Data Visualization</p>
                            </div>
                        </div>
                    </div>

                    <div class="span-3 bg-gray-900 rounded-2xl p-6 card-hover border border-gray-800">
                        <h4 class="text-xl font-bold mb-4 tesla-red">商业应用</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">商业智能</p>
                                <p class="text-small-en mt-1">Business Intelligence</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数据产品设计</p>
                                <p class="text-small-en mt-1">Data Product Design</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">数字营销分析</p>
                                <p class="text-small-en mt-1">Digital Marketing Analytics</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded-lg text-center">
                                <p class="font-bold text-gray-100 text-sm">供应链分析</p>
                                <p class="text-small-en mt-1">Supply Chain Analytics</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 课程体系详解 -->
        <section class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">专业课程体系深度解析</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-code mr-2"></i>
                            编程基础模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">Python编程</p>
                                <p class="text-gray-400 text-xs">数据科学主流语言</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">Java编程</p>
                                <p class="text-gray-400 text-xs">大数据生态核心语言</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">Scala编程</p>
                                <p class="text-gray-400 text-xs">Spark开发语言</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-server mr-2"></i>
                            大数据平台模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">Hadoop生态</p>
                                <p class="text-gray-400 text-xs">HDFS、MapReduce、YARN</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">Spark计算</p>
                                <p class="text-gray-400 text-xs">内存计算框架</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">Kafka消息队列</p>
                                <p class="text-gray-400 text-xs">实时数据流处理</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-database mr-2"></i>
                            数据存储模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">关系型数据库</p>
                                <p class="text-gray-400 text-xs">MySQL、PostgreSQL</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">NoSQL数据库</p>
                                <p class="text-gray-400 text-xs">MongoDB、Redis、HBase</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">数据仓库</p>
                                <p class="text-gray-400 text-xs">Hive、ClickHouse</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-brain mr-2"></i>
                            机器学习模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">监督学习</p>
                                <p class="text-gray-400 text-xs">分类与回归算法</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">无监督学习</p>
                                <p class="text-gray-400 text-xs">聚类与降维算法</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">深度学习</p>
                                <p class="text-gray-400 text-xs">神经网络与深度模型</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-cloud mr-2"></i>
                            云计算模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">AWS云服务</p>
                                <p class="text-gray-400 text-xs">亚马逊云计算平台</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">阿里云服务</p>
                                <p class="text-gray-400 text-xs">国内主流云平台</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">容器技术</p>
                                <p class="text-gray-400 text-xs">Docker、Kubernetes</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <h4 class="text-xl font-bold tesla-red mb-4 flex items-center">
                            <i class="fas fa-chart-pie mr-2"></i>
                            商业应用模块
                        </h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">商业智能BI</p>
                                <p class="text-gray-400 text-xs">Tableau、Power BI</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">A/B测试</p>
                                <p class="text-gray-400 text-xs">产品优化实验设计</p>
                            </div>
                            <div class="p-3 bg-gray-800 rounded">
                                <p class="font-bold text-gray-100">用户画像</p>
                                <p class="text-gray-400 text-xs">精准营销与推荐</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 相关认证简介 -->
        <section id="certifications" class="py-16 px-6">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">相关认证简介</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-cloud text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">AWS认证</h4>
                            <p class="text-sm text-gray-400 mb-3">云计算专业认证</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">亚马逊云服务专业认证，包括数据工程师、机器学习专家等多个方向</p>
                    </div>

                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-database text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">Hadoop认证</h4>
                            <p class="text-sm text-gray-400 mb-3">大数据平台认证</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">Cloudera、Hortonworks等厂商的Hadoop生态系统专业认证</p>
                    </div>

                    <div class="text-center p-6 bg-gray-900 rounded-xl border border-gray-800 card-hover">
                        <div class="mb-4">
                            <i class="fas fa-award text-3xl tesla-red mb-3"></i>
                            <h4 class="text-lg font-bold text-gray-100">CFA</h4>
                            <p class="text-sm text-gray-400 mb-3">特许金融分析师</p>
                        </div>
                        <p class="text-sm text-gray-300 leading-relaxed">全球投资分析领域权威认证，大数据技术与金融分析深度融合，培养复合型数据金融科技人才</p>
                    </div>
                </div>

                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-400 leading-relaxed">
                        以上认证可根据个人职业发展方向选择性考虑，建议结合专业学习进度和实习经验合理规划
                    </p>
                </div>
            </div>
        </section>

        <!-- 专业发展建议 -->
        <section class="py-16 px-6 bg-gray-900/50">
            <div class="max-w-7xl mx-auto">
                <h3 class="text-3xl font-bold mb-8 text-center tesla-red">专业发展核心建议</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">01</div>
                        <h4 class="text-xl font-bold mb-4">强化技术基础</h4>
                        <p class="text-sm mb-4">扎实掌握编程语言和大数据技术栈，建立全栈技术能力</p>
                        <div class="text-xs text-gray-400">
                            <p>• 精通Python、Java编程</p>
                            <p>• 掌握Hadoop、Spark生态</p>
                            <p>• 学习云计算平台技术</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">02</div>
                        <h4 class="text-xl font-bold mb-4">培养商业思维</h4>
                        <p class="text-sm mb-4">结合商业场景理解数据价值，培养数据驱动的商业洞察力</p>
                        <div class="text-xs text-gray-400">
                            <p>• 理解业务需求和痛点</p>
                            <p>• 学习产品设计思维</p>
                            <p>• 提升商业分析能力</p>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="number-big tesla-red mb-4">03</div>
                        <h4 class="text-xl font-bold mb-4">关注前沿趋势</h4>
                        <p class="text-sm mb-4">紧跟大数据和人工智能发展趋势，持续学习新技术</p>
                        <div class="text-xs text-gray-400">
                            <p>• 关注AI技术发展</p>
                            <p>• 学习实时计算技术</p>
                            <p>• 了解边缘计算趋势</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 总结与展望 -->
        <section class="py-16 px-6">
            <div class="max-w-4xl mx-auto text-center bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-12 border border-gray-700">
                <h3 class="text-4xl font-bold mb-6">
                    <span class="tesla-red">数据驱动</span> · 智能未来
                </h3>
                <p class="text-xl mb-8 leading-relaxed">
                    大数据管理与应用专业凭借其技术与商业深度融合的特色，
                    为学生在数字化时代的发展奠定了坚实基础。
                    从海量数据处理到智能商业应用，从传统企业数字化到前沿AI创新，
                    大数据专业毕业生正在用技术力量推动各行各业的数字化转型。
                </p>
                <div class="flex justify-center items-center space-x-8 text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-graduation-cap tesla-red mr-2"></i>
                        <span>专业深度</span>
                    </div>
                    <div class="w-2 h-2 bg-tesla-red rounded-full"></div>
                    <div class="flex items-center">
                        <i class="fas fa-globe tesla-red mr-2"></i>
                        <span>国际视野</span>
                    </div>
                    <div class="w-2 h-2 bg-tesla-red rounded-full"></div>
                    <div class="flex items-center">
                        <i class="fas fa-rocket tesla-red mr-2"></i>
                        <span>创新应用</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="bg-gray-900 py-8 px-6 border-t border-gray-800">
            <div class="max-w-7xl mx-auto text-center">
                <div class="text-sm text-gray-500">
                    <p>&copy; 上海对外经贸大学统计与信息学院 | 大数据管理与应用专业介绍</p>
                    <p class="mt-2">Shanghai University of International Business and Economics - Big Data Management & Application Major</p>
                </div>
            </div>
        </footer>
    </main>

    <!-- 脚本文件 -->
    <script src="../assets/js/common.js"></script>
</body>
</html>
