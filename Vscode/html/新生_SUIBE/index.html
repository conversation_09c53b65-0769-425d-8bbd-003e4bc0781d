<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SUIBE大学新生攻略 - 现代化指南系统</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <meta name="description" content="上海对外经贸大学新生入学完整攻略，包含入学准备、住宿生活、学习指南等全方位信息">
    <meta name="keywords" content="SUIBE,上海对外经贸大学,新生攻略,入学指南,大学生活">
</head>
<body>
    <div class="glass-container">
        <header class="glass-header">
            <h1>SUIBE大学新生攻略</h1>
            <p>上海对外经贸大学新生入学完整指南 - 从录取到毕业的全程陪伴</p>
            
            <!-- 搜索栏 -->
            <div class="search-container">
                <input type="text" placeholder="搜索攻略内容..." class="glass-search" id="searchInput">
                <button class="search-btn" id="searchBtn">🔍</button>
            </div>
        </header>

        <!-- 主要分类网格 -->
        <main class="category-grid enhanced-grid">
            <!-- 入学相关 -->
            <div class="glass-card" onclick="navigateToPage('page1.html')">
                <div class="card-icon">🎓</div>
                <div class="card-content">
                    <h3>入学相关</h3>
                    <p>军训指南、入学考试须知、报到流程详解</p>
                    <div class="card-stats">
                        <span class="file-count">3 个重要事项</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 住宿生活 -->
            <div class="glass-card" onclick="navigateToPage('page2.html')">
                <div class="card-icon">🏠</div>
                <div class="card-content">
                    <h3>住宿生活</h3>
                    <p>宿舍入住攻略、水电费缴纳指南</p>
                    <div class="card-stats">
                        <span class="file-count">2 个生活指南</span>
                    </div>
                </div>
            </div>

            <!-- 选课系统 -->
            <div class="glass-card" onclick="navigateToPage('page3.html')">
                <div class="card-icon">📚</div>
                <div class="card-content">
                    <h3>选课系统</h3>
                    <p>选课操作流程、课程时间安排</p>
                    <div class="card-stats">
                        <span class="file-count">2 个操作指南</span>
                    </div>
                </div>
            </div>

            <!-- 专业学习 -->
            <div class="glass-card" onclick="navigateToPage('page4.html')">
                <div class="card-icon">🔄</div>
                <div class="card-content">
                    <h3>专业学习</h3>
                    <p>转专业、辅修专业申请</p>
                    <div class="card-stats">
                        <span class="file-count">2 个专业选择</span>
                    </div>
                </div>
            </div>

            <!-- 考试指南 -->
            <div class="glass-card" onclick="navigateToPage('page5.html')">
                <div class="card-icon">📝</div>
                <div class="card-content">
                    <h3>考试指南</h3>
                    <p>四六级、国家级考试</p>
                    <div class="card-stats">
                        <span class="file-count">2 个考试类型</span>
                    </div>
                </div>
            </div>

            <!-- 奖学金资助 -->
            <div class="glass-card" onclick="navigateToPage('page6.html')">
                <div class="card-icon">💰</div>
                <div class="card-content">
                    <h3>奖学金资助</h3>
                    <p>奖学金申请、助学金指南</p>
                    <div class="card-stats">
                        <span class="file-count">多种资助方式</span>
                    </div>
                </div>
            </div>

            <!-- 教务服务 -->
            <div class="glass-card" onclick="navigateToPage('page7.html')">
                <div class="card-icon">⚙️</div>
                <div class="card-content">
                    <h3>教务服务</h3>
                    <p>教务系统、学费、教材</p>
                    <div class="card-stats">
                        <span class="file-count">3 个服务项目</span>
                    </div>
                </div>
            </div>
        </main>

        <!-- 图片卡片区域 -->
        <section class="image-cards-section">
            <!-- QQ群卡片 -->
            <div class="glass-card image-card" onclick="window.open('https://qun.qq.com/universal-share/share?ac=1&authKey=w9clmZnE5frHG3hhwbhW09KEIj3WOT5Aul1n2AhDhMPeSiAfoIXH09dZw5Jr3op6&busi_data=eyJncm91cENvZGUiOiIzNDQ3OTE5NTYiLCJ0b2tlbiI6InhUYWh3a0NuZFEva1ZBVUdZNTBxQlB6dUpxM3d6ekgwTm9iUGxRRU94VWdBSktmMXBqa21pQ01jeUx6RmtqVkEiLCJ1aW4iOiIxMDcxMDUyNTE4In0%3D&data=D9MYFXvlNZQMVoo3qCF2_rDaGzj1FH3eO5UAJXbJ6Y8X2IwdE1T5IgcV8zhXR50zP7uYcACydVH0OCVzmHRfdw&svctype=4&tempid=h5_group_info', '_blank')">
                <img src="images/p1.jpg" alt="QQ群二维码" class="card-image">
                <div class="card-content">
                    <h3>加入QQ群</h3>
                    <p>与学长学姐交流，获取最新资讯</p>
                    <div class="card-stats">
                        <span class="file-count">在线交流群</span>
                    </div>
                </div>
            </div>

            <!-- 频道卡片 -->
            <div class="glass-card image-card" onclick="window.open('https://pd.qq.com/s/6s7k5tbcw?b=9', '_blank')">
                <img src="images/p2.jpg" alt="QQ频道二维码" class="card-image">
                <div class="card-content">
                    <h3>关注频道</h3>
                    <p>获取官方通知和重要信息</p>
                    <div class="card-stats">
                        <span class="file-count">官方频道</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 底部信息 -->
        <footer class="glass-footer" style="text-align: center; margin-top: 40px; padding: 20px; border-top: 1px solid rgba(255,255,255,0.1);">
            <p style="color: rgba(255,255,255,0.6); font-size: 14px;">
                💡 提示：点击任意卡片查看详细内容，使用搜索功能快速找到需要的信息，加入QQ群和频道获取更多帮助
            </p>
            <p style="color: rgba(255,255,255,0.4); font-size: 12px; margin-top: 8px;">
                SUIBE新生攻略系统 - 为新生提供全方位入学指导
            </p>
        </footer>
    </div>

    <script src="js/script.js"></script>
    
    <!-- 页面加载动画 -->
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加卡片进入动画
            const cards = document.querySelectorAll('.glass-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100 + 300);
            });
            
            // 添加统计数据动画
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 1000);
            });
        });
        
        // 添加卡片点击反馈效果
        document.querySelectorAll('.glass-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
