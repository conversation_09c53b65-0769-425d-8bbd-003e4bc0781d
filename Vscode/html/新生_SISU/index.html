<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上外新生攻略 - 现代化指南系统</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <meta name="description" content="上海外国语大学新生入学完整攻略，包含入学准备、学术相关、政策制度、校园文化、校园服务、校园生活等全方位信息">
    <meta name="keywords" content="上外,上海外国语大学,新生攻略,入学指南,大学生活,SISU">
</head>
<body>
    <div class="glass-container">
        <header class="glass-header">
            <h1>上外新生攻略</h1>
            <p>上海外国语大学新生入学完整指南 - 格高志远，学贯中外</p>
            
            <!-- 搜索栏 -->
            <div class="search-container">
                <input type="text" placeholder="搜索攻略内容..." class="glass-search" id="searchInput">
                <button class="search-btn" id="searchBtn">🔍</button>
            </div>
        </header>

        <!-- 主要分类网格 -->
        <main class="category-grid enhanced-grid">
            <!-- 入学准备 -->
            <div class="glass-card" onclick="navigateToPage('入学准备.html')">
                <div class="card-icon">🎓</div>
                <div class="card-content">
                    <h3>入学准备</h3>
                    <p>大学名词解释、军训指南、入学流程、时间安排、新生建议</p>
                    <div class="card-stats">
                        <span class="file-count">6 个重要事项</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 90%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学术相关 -->
            <div class="glass-card" onclick="navigateToPage('学术相关.html')">
                <div class="card-icon">📚</div>
                <div class="card-content">
                    <h3>学术相关</h3>
                    <p>选课指南、院系介绍、转专业、辅修情况、教材购买</p>
                    <div class="card-stats">
                        <span class="file-count">5 个学术指南</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 政策制度 -->
            <div class="glass-card" onclick="navigateToPage('政策制度.html')">
                <div class="card-icon">📋</div>
                <div class="card-content">
                    <h3>政策制度</h3>
                    <p>学校规章制度、学分制度、考试制度、奖惩制度</p>
                    <div class="card-stats">
                        <span class="file-count">4 个制度规范</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 80%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 校园文化 -->
            <div class="glass-card" onclick="navigateToPage('校园文化.html')">
                <div class="card-icon">🎭</div>
                <div class="card-content">
                    <h3>校园文化</h3>
                    <p>学校历史、校训精神、文化传统、学术氛围</p>
                    <div class="card-stats">
                        <span class="file-count">4 个文化主题</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 校园服务 -->
            <div class="glass-card" onclick="navigateToPage('校园服务.html')">
                <div class="card-icon">🏢</div>
                <div class="card-content">
                    <h3>校园服务</h3>
                    <p>图书馆、食堂、医务室、银行、快递、网络服务</p>
                    <div class="card-stats">
                        <span class="file-count">6 个服务项目</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 88%;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 校园生活 -->
            <div class="glass-card" onclick="navigateToPage('校园生活.html')">
                <div class="card-icon">🏠</div>
                <div class="card-content">
                    <h3>校园生活</h3>
                    <p>宿舍生活、社团活动、体育设施、娱乐休闲</p>
                    <div class="card-stats">
                        <span class="file-count">4 个生活方面</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 82%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 图片卡片区域 -->
        <section class="image-cards-section">
            <!-- 图片卡片1 - 仅显示图片 -->
            <div class="glass-card image-card">
                <img src="images/1.jpg" alt="上外校园图片" class="card-image">
                <div class="card-content">
                    <h3>校园风光</h3>
                    <p>上海外国语大学美丽校园</p>
                    <div class="card-stats">
                        <span class="file-count">校园展示</span>
                    </div>
                </div>
            </div>

            <!-- 图片卡片2 - 带链接功能 -->
            <div class="glass-card image-card" onclick="window.open('https://pd.qq.com/g/655251834053549594', '_blank')">
                <img src="images/2.jpg" alt="QQ频道二维码" class="card-image">
                <div class="card-content">
                    <h3>加入频道</h3>
                    <p>点击加入上外新生交流频道</p>
                    <div class="card-stats">
                        <span class="file-count">在线交流</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 底部信息 -->
        <footer class="glass-footer" style="text-align: center; margin-top: 40px; padding: 20px; border-top: 1px solid rgba(255,255,255,0.1);">
            <p style="color: rgba(255,255,255,0.6); font-size: 14px;">
                💡 提示：点击任意卡片查看详细内容，使用搜索功能快速找到需要的信息
            </p>
            <p style="color: rgba(255,255,255,0.4); font-size: 12px; margin-top: 8px;">
                上外新生攻略系统 - 为新生提供全方位入学指导 | 上海外国语大学
            </p>
        </footer>
    </div>

    <script src="js/script.js"></script>
    
    <!-- 页面加载动画 -->
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加卡片进入动画
            const cards = document.querySelectorAll('.glass-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100 + 300);
            });
            
            // 添加统计数据动画
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 1000);
            });
        });
        
        // 添加卡片点击反馈效果
        document.querySelectorAll('.glass-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
