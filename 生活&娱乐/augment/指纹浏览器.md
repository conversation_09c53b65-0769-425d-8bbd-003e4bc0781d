# Augment-725

> 原文链接：https://www.cnblogs.com/xiaocheng12138/articles/19011306
> 发布时间：2025-07-29 17:53
> 作者：xiaocheng12138

## 📌 适用对象

适合**动手能力强**的用户（想省事可联系群主获取成品号）

## 🔧 步骤1：环境准备

### 1.1 下载指纹浏览器

**工具**：[AdsPower Browser](https://www.adspower.net/download)（防关联）

![AdsPower Browser界面](生活&娱乐/图片/2506661-20250729172709129-1963245892.png)

**操作**：
- 安装后打开软件
- 点击「新建浏览器配置」

### 1.2 配置代理

**代理类型**：HTTP

**地址/端口**：填写你的**VPN软件开放ip和端口**（如局域网本机ip:7897）

**验证IP**：点击测试
- 确保显示新加坡

![代理配置界面1](生活&娱乐/图片/2506661-20250729172744675-1199829766.png)

![代理配置界面2](生活&娱乐/图片/2506661-20250729172808787-151437832.png)

## 📝 步骤2：注册账号

### 2.1 访问官网

**Augment注册页**：
```
https://www.augmentcode.com/
```

### 2.2 填写信息

**邮箱**：推荐使用**域名邮箱**（进群获取教程 群号：1029341429）

**密码**：大写+小写+数字（例：Aug2024!）

**⚠️ 重要**：关闭浏览器前**保存账号密码**！

![注册页面1](生活&娱乐/图片/2506661-20250729172830166-366133083.png)

![注册页面2](生活&娱乐/图片/2506661-20250729172851044-689193995.png)

## 🎁 步骤3：领取福利

### 3.1 福利链接

**活动地址**：（先到先得，随时可能失效）
```
https://app.augmentcode.com/promotions/cursor
```

### 3.2 提交验证

**选项1**：上传**真实账单截图**（Windsurf/Cursor账单，需含日期+金额≥$10）

**选项2**：用工具生成账单（推荐https://wipdf.vercel.app/）

**要求**：账单用户名需匹配邮箱（如邮箱 <EMAIL> → 账单用户 <EMAIL>）

### 3.3 完成审核

提交后**刷新页面**，即可看到 **725次对话额度**
- 若未显示，检查邮箱是否收到验证通知

![审核完成界面](生活&娱乐/图片/2506661-20250729172919766-333869881.png)

## ⚠️ 关键提醒

**代理稳定**：全程保持**新加坡IP**，勿切换国家！

**账单要求**：
- 日期：**3天内**
- 金额：**$10-50**
- 需显示**服务名称**（如 Cursor Pro）

**安全建议**：
- 使用**虚拟机/沙盒环境**运行AdsPower
- 每完成一个账号后**清理浏览器缓存**（AdsPower内置「一键清理」功能）

## 💡 常见问题

### ❓ 审核失败怎么办？

- 检查IP是否纯净（IP检测）
- 更换账单截图（避免重复使用）

### ❓ 没有域名邮箱？

进群获取替代方案：QQ群：1029341429

![QQ群二维码](生活&娱乐/图片/2506661-20250729173251564-1701827033.png)

---

**本文作者**：xiaocheng12138  
**本文链接**：https://www.cnblogs.com/xiaocheng12138/articles/19011306  
**版权声明**：本博客所有文章除特别声明外，均采用 BY-NC-SA 许可协议。转载请注明出处！
