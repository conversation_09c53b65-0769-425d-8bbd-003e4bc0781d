# 饥荒

## 脚本id

**require("debugcommands")d_resetskilltree()**

系统名root



系统密码Qazwsxedc!123

12312312

用户名 ubuntu

---



普通密码 qazwsxedc

第一次输入

minimap = TheSim:FindFirstEntityWithTag(&#34;minimap&#34;)TheWorld.minimap.MiniMapt:ShowArea (0,0,0,10000)

Ass第二次输入

for x=-1600,1600,35 do for y=-1600,1600,35 do ThePlayer.player_classified.MapExplorer:RevealArea(x,0,y) end end

362175979 虫洞
378160973 全球定位
501385076 quickpick
661253977灵魂携带
==666155465 show me==
==1185229307 boss血量==
==1207269058 简易血量条==
1216718131 防卡两招
1271089343 复活
==1530801499 木牌传送==
1595631294 木箱带小木牌
==2189004162 insight==
2281925291Finder 高亮查找

## 相关信息

公网IP：*************

科雷秘钥：pds-g^KU__CRUc-V6^/Z3+gjC47OwQt1m7MeTveVsibB6eCaKTreaINABW7HI=

管理员我id：KU__CRUc-V6

管理员我id：KU_DvYwVRh4

### 地面世界设置

return {
  custom_settings_desc="",
  custom_settings_id="CUSTOM_1",
  custom_settings_name="1",
  custom_worldgen_desc="",
  custom_worldgen_id="CUSTOM_1",
  custom_worldgen_name="1",
  desc="标准《饥荒》体验。",
  hideminimap=false,
  id="SURVIVAL_TOGETHER",
  location="forest",
  max_playlist_position=999,
  min_playlist_position=0,
  name="生存",
  numrandom_set_pieces=4,
  override_level_string=false,
  overrides={
    alternatehunt="default",
    angrybees="default",
    antliontribute="default",
    autumn="default",
    bananabush_portalrate="default",
    basicresource_regrowth="none",
    bats_setting="default",
    bearger="default",
    beefalo="default",
    beefaloheat="default",
    beequeen="default",
    bees="default",
    bees_setting="default",
    berrybush="default",
    birds="default",
    boons="default",
    branching="most",
    brightmarecreatures="default",
    bunnymen_setting="default",
    butterfly="default",
    buzzard="default",
    cactus="default",
    cactus_regrowth="default",
    carrot="default",
    carrots_regrowth="default",
    catcoon="default",
    catcoons="default",
    chess="default",
    cookiecutters="default",
    crabking="default",
    crow_carnival="default",
    darkness="default",
    day="default",
    daywalker2="default",
    deciduousmonster="default",
    deciduoustree_regrowth="default",
    deerclops="default",
    dragonfly="default",
    dropeverythingondespawn="default",
    evergreen_regrowth="default",
    extrastartingitems="default",
    eyeofterror="default",
    fishschools="default",
    flint="default",
    flowers="default",
    flowers_regrowth="default",
    frograin="default",
    frogs="default",
    fruitfly="default",
    ghostenabled="always",
    ghostsanitydrain="always",
    gnarwail="default",
    goosemoose="default",
    grass="default",
    grassgekkos="never",
    hallowed_nights="default",
    has_ocean=true,
    healthpenalty="always",
    hound_mounds="default",
    houndmound="default",
    hounds="default",
    hunger="default",
    hunt="default",
    junkyard="default",
    keep_disconnected_tiles=true,
    klaus="default",
    krampus="default",
    layout_mode="LinkNodesByKeys",
    lessdamagetaken="none",
    liefs="always",
    lightcrab_portalrate="default",
    lightning="default",
    lightninggoat="default",
    loop="default",
    lunarhail_frequency="default",
    lureplants="default",
    malbatross="default",
    marshbush="default",
    merm="default",
    merms="default",
    meteorshowers="always",
    meteorspawner="default",
    moles="default",
    moles_setting="default",
    monkeytail_portalrate="default",
    moon_berrybush="default",
    moon_bullkelp="default",
    moon_carrot="default",
    moon_fissure="default",
    moon_fruitdragon="default",
    moon_hotspring="default",
    moon_rock="default",
    moon_sapling="default",
    moon_spider="default",
    moon_spiders="default",
    moon_starfish="default",
    moon_tree="default",
    moon_tree_regrowth="default",
    mosquitos="default",
    mushroom="default",
    mutated_hounds="default",
    no_joining_islands=true,
    no_wormholes_to_disconnected_tiles=true,
    ocean_bullkelp="default",
    ocean_otterdens="default",
    ocean_seastack="ocean_default",
    ocean_shoal="default",
    ocean_waterplant="ocean_default",
    ocean_wobsterden="default",
    otters_setting="default",
    palmcone_seed_portalrate="default",
    palmconetree="default",
    palmconetree_regrowth="default",
    penguins="default",
    penguins_moon="default",
    perd="default",
    petrification="default",
    pigs="default",
    pigs_setting="default",
    pirateraids="default",
    ponds="default",
    portal_spawnrate="default",
    portalresurection="none",
    powder_monkey_portalrate="default",
    prefabswaps_start="classic",
    rabbits="default",
    rabbits_setting="default",
    reeds="default",
    reeds_regrowth="default",
    regrowth="never",
    resettime="none",
    rifts_enabled="default",
    rifts_frequency="default",
    roads="default",
    rock="default",
    rock_ice="default",
    saltstack_regrowth="default",
    sapling="default",
    season_start="default",
    seasonalstartingitems="default",
    shadowcreatures="default",
    sharkboi="default",
    sharks="default",
    spawnmode="fixed",
    spawnprotection="default",
    specialevent="default",
    spider_warriors="default",
    spiderqueen="default",
    spiders="default",
    spiders_setting="default",
    spring="default",
    squid="default",
    stageplays="default",
    start_location="default",
    summer="default",
    summerhounds="default",
    tallbirds="default",
    task_set="default",
    temperaturedamage="default",
    tentacles="default",
    terrariumchest="default",
    touchstone="default",
    trees="default",
    tumbleweed="default",
    twiggytrees_regrowth="never",
    walrus="default",
    walrus_setting="default",
    wasps="default",
    weather="default",
    wildfires="default",
    winter="default",
    winterhounds="default",
    winters_feast="enabled",
    wobsters="default",
    world_size="small",
    wormhole_prefab="wormhole",
    year_of_the_beefalo="default",
    year_of_the_bunnyman="default",
    year_of_the_carrat="default",
    year_of_the_catcoon="default",
    year_of_the_dragonfly="default",
    year_of_the_gobbler="default",
    year_of_the_pig="default",
    year_of_the_snake="default",
    year_of_the_varg="default" 
  },
  playstyle="survival",
  random_set_pieces={
    "Sculptures_2",
    "Sculptures_3",
    "Sculptures_4",
    "Sculptures_5",
    "Chessy_1",
    "Chessy_2",
    "Chessy_3",
    "Chessy_4",
    "Chessy_5",
    "Chessy_6",
    "Maxwell1",
    "Maxwell2",
    "Maxwell3",
    "Maxwell4",
    "Maxwell6",
    "Maxwell7",
    "Warzone_1",
    "Warzone_2",
    "Warzone_3" 
  },
  required_prefabs={ "multiplayer_portal" },
  required_setpieces={ "Sculptures_1", "Maxwell5" },
  settings_desc="标准《饥荒》体验。",
  settings_id="SURVIVAL_TOGETHER",
  settings_name="生存",
  substitutes={  },
  version=4,
  worldgen_desc="标准《饥荒》体验。",
  worldgen_id="SURVIVAL_TOGETHER",
  worldgen_name="生存" 
}

### 洞穴世界设置

return {
  background_node_range={ 0, 1 },
  desc="探查洞穴…… 一起！",
  hideminimap=false,
  id="DST_CAVE",
  location="cave",
  max_playlist_position=999,
  min_playlist_position=0,
  name="洞穴",
  numrandom_set_pieces=0,
  override_level_string=false,
  overrides={
    acidrain_enabled="always",
    atriumgate="default",
    banana="default",
    basicresource_regrowth="none",
    bats="default",
    bats_setting="default",
    beefaloheat="default",
    berrybush="default",
    boons="default",
    branching="default",
    brightmarecreatures="default",
    bunnymen="default",
    bunnymen_setting="default",
    cave_ponds="default",
    cave_spiders="default",
    cavelight="default",
    chess="default",
    chest_mimics="default",
    crow_carnival="default",
    darkness="default",
    day="default",
    daywalker="default",
    dropeverythingondespawn="default",
    dustmoths="default",
    earthquakes="default",
    extrastartingitems="default",
    fern="default",
    fissure="default",
    flint="default",
    flower_cave="default",
    flower_cave_regrowth="default",
    fruitfly="default",
    ghostenabled="always",
    ghostsanitydrain="always",
    grass="default",
    grassgekkos="default",
    hallowed_nights="default",
    healthpenalty="always",
    hunger="default",
    itemmimics="default",
    krampus="default",
    layout_mode="RestrictNodesByKey",
    lessdamagetaken="none",
    lichen="default",
    liefs="default",
    lightflier_flower_regrowth="default",
    lightfliers="default",
    loop="default",
    marshbush="default",
    merms="default",
    molebats="default",
    moles_setting="default",
    monkey="default",
    monkey_setting="default",
    mushgnome="default",
    mushroom="default",
    mushtree="default",
    mushtree_moon_regrowth="default",
    mushtree_regrowth="default",
    nightmarecreatures="default",
    pigs_setting="default",
    portalresurection="none",
    prefabswaps_start="default",
    reeds="default",
    regrowth="default",
    resettime="none",
    rifts_enabled_cave="default",
    rifts_frequency_cave="default",
    roads="never",
    rock="default",
    rocky="default",
    rocky_setting="default",
    sapling="default",
    season_start="default",
    seasonalstartingitems="default",
    shadowcreatures="default",
    slurper="default",
    slurtles="default",
    slurtles_setting="default",
    snurtles="default",
    spawnmode="fixed",
    spawnprotection="default",
    specialevent="default",
    spider_dropper="default",
    spider_hider="default",
    spider_spitter="default",
    spider_warriors="default",
    spiderqueen="default",
    spiders="default",
    spiders_setting="default",
    start_location="caves",
    task_set="cave_default",
    temperaturedamage="default",
    tentacles="default",
    toadstool="default",
    touchstone="default",
    trees="default",
    weather="default",
    winters_feast="enabled",
    world_size="default",
    wormattacks="default",
    wormattacks_boss="default",
    wormhole_prefab="tentacle_pillar",
    wormlights="default",
    worms="default",
    year_of_the_beefalo="default",
    year_of_the_bunnyman="default",
    year_of_the_carrat="default",
    year_of_the_catcoon="default",
    year_of_the_dragonfly="default",
    year_of_the_gobbler="default",
    year_of_the_pig="default",
    year_of_the_snake="default",
    year_of_the_varg="default" 
  },
  required_prefabs={ "multiplayer_portal" },
  settings_desc="探查洞穴…… 一起！",
  settings_id="DST_CAVE",
  settings_name="洞穴",
  substitutes={  },
  version=4,
  worldgen_desc="探查洞穴…… 一起！",
  worldgen_id="DST_CAVE",
  worldgen_name="洞穴" 
}

### mod设置

return {
  ["workshop-1185229307"]={
    configuration_options={
      CAMERA=true,
      CAPTURE=false,
      DAMAGE_NUMBERS=true,
      DAMAGE_RESISTANCE=true,
      FRAME_PHASES=true,
      GLOBAL=false,
      GLOBAL_NUMBERS=false,
      HEADER_CLIENT=false,
      HEADER_SERVER=false,
      HORIZONTAL_OFFSET=0,
      TAG="EPIC",
      TRANSLATOR=false,
      WETNESS_METER=false 
    },
    enabled=true 
  },
  ["workshop-1207269058"]={ configuration_options={  }, enabled=true },
  ["workshop-1216718131"]={ configuration_options={ clean=true, lang=true, stack=true }, enabled=true },
  ["workshop-1271089343"]={
    configuration_options={ bekkitt_skeleton_pen=0, null_option=true, skeleton_player=true },
    enabled=true 
  },
  ["workshop-1530801499"]={
    configuration_options={
      ArrowsignEnable=false,
      CountdownEnable=false,
      HungerCost=1,
      Ownership=true,
      SanityCost=1 
    },
    enabled=true 
  },
  ["workshop-1595631294"]={
    configuration_options={
      BundleItems=false,
      ChangeSkin=true,
      Digornot=false,
      DragonflyChest=false,
      Icebox=false,
      SaltBox=false 
    },
    enabled=true 
  },
  ["workshop-2189004162"]={
    configuration_options={
      DEBUG_ENABLED=false,
      DEBUG_SHOW_DISABLED=false,
      DEBUG_SHOW_NOTIMPLEMENTED=false,
      DEBUG_SHOW_PREFAB=false,
      alt_only_information=false,
      appeasement_value="undefined",
      armor="undefined",
      attack_range_type="undefined",
      battlesong_range="both",
      blink_range=false,
      boss_indicator=true,
      bottle_indicator=true,
      crash_reporter=false,
      danger_announcements="undefined",
      death_indicator=false,
      display_attack_range="undefined",
      display_batwave_information="undefined",
      display_cawnival="undefined",
      display_compostvalue="undefined",
      display_crafting_lookup_button=true,
      display_fertilizer="undefined",
      display_finiteuses=true,
      display_food="undefined",
      display_gyminfo="undefined",
      display_harvestable=true,
      display_health="undefined",
      display_hunger="undefined",
      display_insight_menu_button=true,
      display_itemmimic_information="undefined",
      display_mob_attack_damage="undefined",
      display_oceanfishing="undefined",
      display_perishable="undefined",
      display_pickable=true,
      display_plant_stressors="undefined",
      display_pollination="undefined",
      display_rabbitking_information="undefined",
      display_rechargeable="undefined",
      display_sanity="undefined",
      display_sanity_interactions="undefined",
      display_sanityaura="undefined",
      display_shadowthrall_information="undefined",
      display_shared_stats="undefined",
      display_shelter_info="undefined",
      display_simplefishing="undefined",
      display_spawner_information="undefined",
      display_tackle_information="undefined",
      display_timers="undefined",
      display_unwrappable="undefined",
      display_upgradeable="undefined",
      display_weather="undefined",
      display_weighable="undefined",
      display_world_events="undefined",
      display_worldmigrator="undefined",
      display_yotb_appraisal="undefined",
      display_yotb_winners="undefined",
      domestication_information="undefined",
      experimental_highlighting=true,
      follower_info="undefined",
      followtext_insight_font_size=28,
      food_effects=true,
      food_memory="undefined",
      food_order="interface",
      food_style="long",
      food_units=true,
      fuel_highlighting=false,
      fuel_highlighting_color="RED",
      fuel_verbosity="undefined",
      growth_verbosity="undefined",
      herd_information="undefined",
      highlighting=true,
      highlighting_color="GREEN",
      highlighting_darkness=true,
      hover_range_indicator=true,
      hoverer_insight_font_size=30,
      hoverer_line_truncation="None",
      hunt_indicator="undefined",
      info_preload="undefined",
      info_style="text",
      insight_font="UIFONT",
      inventorybar_insight_font_size=25,
      item_worth="undefined",
      itemtile_display="percentages",
      klaus_sack_info="undefined",
      klaus_sack_markers="undefined",
      language="automatic",
      lightningrod_range=1,
      miniboss_indicator=true,
      naughtiness_verbosity="undefined",
      nightmareclock_display="undefined",
      notable_indicator=true,
      orchestrina_indicator="undefined",
      pipspook_indicator=true,
      refresh_delay="undefined",
      repair_values="undefined",
      sinkhole_marks=2,
      soil_moisture=2,
      soil_nutrients="undefined",
      soil_nutrients_needs_hat="undefined",
      stewer_chef="undefined",
      suspicious_marble_indicator=false,
      temperature_units="game",
      text_coloring=true,
      time_style="realtime_short",
      tumbleweed_info="undefined",
      weapon_damage="undefined",
      weather_detail="undefined",
      wortox_soul_range=true,
      wx78_scanner_info="undefined",
      ["Ŀ�Ɓ�Ǝ�ň�"]=0,
      ["ƌ�Ǥ�ř�"]=0,
      ["Ɲ�ɡ�"]=0,
      ["Ơ�ż�"]=0,
      ["Ȱ�ȯ�"]=0,
      ["ɣ�ǉ�Ǜ�Ņ�"]=0 
    },
    enabled=true 
  },
  ["workshop-2281925291"]={
    configuration_options={ ACTIVEITEM=true, ["Client and server options"]=0, INGREDIENT=true, TINT=1 },
    enabled=true 
  },
  ["workshop-2371484058"]={
    configuration_options={
      [""]=0,
      FoodSp=0,
      UR_day=true,
      beard_sack=false,
      beargerfur_sack=false,
      beargerfur_sack_up=false,
      birdcage_up=true,
      containers_upgraded=false,
      cookpot_up=true,
      fsbox=0,
      icebox_up=2,
      itemtype_b=false,
      itemtype_i=false,
      itemtype_m=false,
      itemtype_s=false,
      krampus_sack=false,
      mrlt=0,
      other_fx=false,
      other_list=false,
      saltbox_up=false,
      seedpouch=false,
      sis_dt=false,
      slbox=0 
    },
    enabled=true 
  },
  ["workshop-2484725102"]={
    configuration_options={
      [""]=false,
      ALLCANUPG=false,
      BACKPACK=false,
      BACKPACKPAGE=3,
      CHANGESIZE=false,
      CLOSEBTN=false,
      C_BATTLESONG_CONTAINER=false,
      C_BEARGERFUR_SACK=false,
      C_BOAT_ANCIENT_CONTAINER=false,
      C_BOOKSTATION=false,
      C_CHESTER=false,
      C_DRAGONFLYCHEST=false,
      C_FISH_BOX=false,
      C_ICEBOX=false,
      C_OCEAN_TRAWLER=false,
      C_OFFERING_POT=false,
      C_OFFERING_POT_UPGRADED=false,
      C_RABBITKINGHORN_CONTAINER=false,
      C_SALTBOX=false,
      C_SHADOW_CONTAINER=false,
      C_SUPERTACKLECONTAINER=false,
      C_TACKLECONTAINER=false,
      C_TREASURECHEST=true,
      DEBUG_IIC=false,
      DEBUG_MAXLV=true,
      DEGRADABLE=false,
      DRAGGABLE=false,
      DROPALL=false,
      EXPENSIVE_BACKPACK=false,
      FILLBTN=false,
      INSIGHT=false,
      KRAMPUS_ONLY=false,
      MAX_LV=5,
      OVERFLOW=true,
      PACKSTYLE=true,
      PAGEABLE=false,
      RETURNITEM=false,
      SCALE_FACTOR=3,
      SEARCHBAR=true,
      SHOWALLPAGE=false,
      SHOWGUIDE=3,
      SORTITEM=false,
      SPECIAL_CONTAINER=false,
      SPECIAL_CONTAINER_C=false,
      SPECIAL_INVENTORY=false,
      UI_BGIMAGE=false,
      UI_ICEBOX=true,
      UI_WIDGETPOS=true,
      UNCOM_MODE=false,
      UPG_MODE=1 
    },
    enabled=true 
  },
  ["workshop-3350516404"]={
    configuration_options={
      BASIC_RESOURCES_ONLY=true,
      CHECK_NESTED_CONTAINERS=true,
      ENABLE_BLACKLIST=true,
      ENABLE_RESOURCE_THRESHOLD=true,
      FILTER_ARMOR=true,
      FILTER_CLOTHES=true,
      FILTER_COOKS=true,
      FILTER_FOOD=false,
      FILTER_GEM=false,
      FILTER_MATERIAL=false,
      FILTER_TOOL=true,
      FILTER_WEAPON=true,
      PERMISSION_MODE="ALL",
      PROTECTED_SLOTS={ 11, 12, 13, 14, 15 },
      RESOURCE_THRESHOLD_AMOUNT=20,
      SEARCH_RANGE=15,
      STASH_KEY="KEY_Z" 
    },
    enabled=true 
  },
  ["workshop-362175979"]={ configuration_options={ ["Draw over FoW"]="disabled" }, enabled=true },
  ["workshop-378160973"]={
    configuration_options={
      ENABLEPINGS=true,
      FIREOPTIONS=2,
      OVERRIDEMODE=false,
      SHAREMINIMAPPROGRESS=true,
      SHOWFIREICONS=true,
      SHOWPLAYERICONS=true,
      SHOWPLAYERSOPTIONS=2 
    },
    enabled=true 
  },
  ["workshop-501385076"]={
    configuration_options={
      quick_cook_on_fire=true,
      quick_harvest=true,
      quick_pick_cactus=true,
      quick_pick_plant_normal_ground=true 
    },
    enabled=true 
  },
  ["workshop-661253977"]={
    configuration_options={ amudiao=true, baodiao=1, kong=0, nillots=0, rendiao=2, zbdiao=true },
    enabled=true 
  } 
}

## 备份

 [cluster_token.txt](/Users/<USER>/Documents/Klei/DoNotStarveTogether/922177481/MyDediServer/cluster_token.txt) 

1. 重命名cluster ：MyDediServer
2. 秘钥文件名：cluster_token.txt
3. 压缩

## 维护

### 云服务器维护

1. 重启==云服务器==：使用终端： ./dstStart.sh  
2. 选择3

### 网站维护

1. 网址：http://47.83.23.133:8081/#/setting/index
2. 账号：admin
3. 密码：

